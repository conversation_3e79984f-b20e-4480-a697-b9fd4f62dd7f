torchvision-0.22.1+cu118.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
torchvision-0.22.1+cu118.dist-info/LICENSE,,
torchvision-0.22.1+cu118.dist-info/METADATA,,
torchvision-0.22.1+cu118.dist-info/RECORD,,
torchvision-0.22.1+cu118.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
torchvision-0.22.1+cu118.dist-info/WHEEL,,
torchvision-0.22.1+cu118.dist-info/top_level.txt,,
torchvision-0.22.1+cu118.dist-info\LICENSE,sha256=wGNj-dM2J9xRc7E1IkRMyF-7Rzn2PhbUWH1cChZbWx4,1546
torchvision-0.22.1+cu118.dist-info\METADATA,sha256=Zd_V4wuwJiQcIDf3J2Us8BUrrLb0iJbDuoeZxMTsgio,6293
torchvision-0.22.1+cu118.dist-info\RECORD,,
torchvision-0.22.1+cu118.dist-info\WHEEL,sha256=fsW6--WFfuzX2scefE6JfcSZ5dXg5h59u8lqlpL5uuo,101
torchvision-0.22.1+cu118.dist-info\top_level.txt,sha256=ucJZoaluBW9BGYT4TuCE6zoZY_JuSP30wbDh-IRpxUU,12
torchvision/_C.pyd,,
torchvision/__init__.py,,
torchvision/__pycache__/__init__.cpython-310.pyc,,
torchvision/__pycache__/_internally_replaced_utils.cpython-310.pyc,,
torchvision/__pycache__/_meta_registrations.cpython-310.pyc,,
torchvision/__pycache__/_utils.cpython-310.pyc,,
torchvision/__pycache__/extension.cpython-310.pyc,,
torchvision/__pycache__/utils.cpython-310.pyc,,
torchvision/__pycache__/version.cpython-310.pyc,,
torchvision/_internally_replaced_utils.py,,
torchvision/_meta_registrations.py,,
torchvision/_utils.py,,
torchvision/cudart64_110.dll,,
torchvision/datasets/__init__.py,,
torchvision/datasets/__pycache__/__init__.cpython-310.pyc,,
torchvision/datasets/__pycache__/_optical_flow.cpython-310.pyc,,
torchvision/datasets/__pycache__/_stereo_matching.cpython-310.pyc,,
torchvision/datasets/__pycache__/caltech.cpython-310.pyc,,
torchvision/datasets/__pycache__/celeba.cpython-310.pyc,,
torchvision/datasets/__pycache__/cifar.cpython-310.pyc,,
torchvision/datasets/__pycache__/cityscapes.cpython-310.pyc,,
torchvision/datasets/__pycache__/clevr.cpython-310.pyc,,
torchvision/datasets/__pycache__/coco.cpython-310.pyc,,
torchvision/datasets/__pycache__/country211.cpython-310.pyc,,
torchvision/datasets/__pycache__/dtd.cpython-310.pyc,,
torchvision/datasets/__pycache__/eurosat.cpython-310.pyc,,
torchvision/datasets/__pycache__/fakedata.cpython-310.pyc,,
torchvision/datasets/__pycache__/fer2013.cpython-310.pyc,,
torchvision/datasets/__pycache__/fgvc_aircraft.cpython-310.pyc,,
torchvision/datasets/__pycache__/flickr.cpython-310.pyc,,
torchvision/datasets/__pycache__/flowers102.cpython-310.pyc,,
torchvision/datasets/__pycache__/folder.cpython-310.pyc,,
torchvision/datasets/__pycache__/food101.cpython-310.pyc,,
torchvision/datasets/__pycache__/gtsrb.cpython-310.pyc,,
torchvision/datasets/__pycache__/hmdb51.cpython-310.pyc,,
torchvision/datasets/__pycache__/imagenet.cpython-310.pyc,,
torchvision/datasets/__pycache__/imagenette.cpython-310.pyc,,
torchvision/datasets/__pycache__/inaturalist.cpython-310.pyc,,
torchvision/datasets/__pycache__/kinetics.cpython-310.pyc,,
torchvision/datasets/__pycache__/kitti.cpython-310.pyc,,
torchvision/datasets/__pycache__/lfw.cpython-310.pyc,,
torchvision/datasets/__pycache__/lsun.cpython-310.pyc,,
torchvision/datasets/__pycache__/mnist.cpython-310.pyc,,
torchvision/datasets/__pycache__/moving_mnist.cpython-310.pyc,,
torchvision/datasets/__pycache__/omniglot.cpython-310.pyc,,
torchvision/datasets/__pycache__/oxford_iiit_pet.cpython-310.pyc,,
torchvision/datasets/__pycache__/pcam.cpython-310.pyc,,
torchvision/datasets/__pycache__/phototour.cpython-310.pyc,,
torchvision/datasets/__pycache__/places365.cpython-310.pyc,,
torchvision/datasets/__pycache__/rendered_sst2.cpython-310.pyc,,
torchvision/datasets/__pycache__/sbd.cpython-310.pyc,,
torchvision/datasets/__pycache__/sbu.cpython-310.pyc,,
torchvision/datasets/__pycache__/semeion.cpython-310.pyc,,
torchvision/datasets/__pycache__/stanford_cars.cpython-310.pyc,,
torchvision/datasets/__pycache__/stl10.cpython-310.pyc,,
torchvision/datasets/__pycache__/sun397.cpython-310.pyc,,
torchvision/datasets/__pycache__/svhn.cpython-310.pyc,,
torchvision/datasets/__pycache__/ucf101.cpython-310.pyc,,
torchvision/datasets/__pycache__/usps.cpython-310.pyc,,
torchvision/datasets/__pycache__/utils.cpython-310.pyc,,
torchvision/datasets/__pycache__/video_utils.cpython-310.pyc,,
torchvision/datasets/__pycache__/vision.cpython-310.pyc,,
torchvision/datasets/__pycache__/voc.cpython-310.pyc,,
torchvision/datasets/__pycache__/widerface.cpython-310.pyc,,
torchvision/datasets/_optical_flow.py,,
torchvision/datasets/_stereo_matching.py,,
torchvision/datasets/caltech.py,,
torchvision/datasets/celeba.py,,
torchvision/datasets/cifar.py,,
torchvision/datasets/cityscapes.py,,
torchvision/datasets/clevr.py,,
torchvision/datasets/coco.py,,
torchvision/datasets/country211.py,,
torchvision/datasets/dtd.py,,
torchvision/datasets/eurosat.py,,
torchvision/datasets/fakedata.py,,
torchvision/datasets/fer2013.py,,
torchvision/datasets/fgvc_aircraft.py,,
torchvision/datasets/flickr.py,,
torchvision/datasets/flowers102.py,,
torchvision/datasets/folder.py,,
torchvision/datasets/food101.py,,
torchvision/datasets/gtsrb.py,,
torchvision/datasets/hmdb51.py,,
torchvision/datasets/imagenet.py,,
torchvision/datasets/imagenette.py,,
torchvision/datasets/inaturalist.py,,
torchvision/datasets/kinetics.py,,
torchvision/datasets/kitti.py,,
torchvision/datasets/lfw.py,,
torchvision/datasets/lsun.py,,
torchvision/datasets/mnist.py,,
torchvision/datasets/moving_mnist.py,,
torchvision/datasets/omniglot.py,,
torchvision/datasets/oxford_iiit_pet.py,,
torchvision/datasets/pcam.py,,
torchvision/datasets/phototour.py,,
torchvision/datasets/places365.py,,
torchvision/datasets/rendered_sst2.py,,
torchvision/datasets/samplers/__init__.py,,
torchvision/datasets/samplers/__pycache__/__init__.cpython-310.pyc,,
torchvision/datasets/samplers/__pycache__/clip_sampler.cpython-310.pyc,,
torchvision/datasets/samplers/clip_sampler.py,,
torchvision/datasets/sbd.py,,
torchvision/datasets/sbu.py,,
torchvision/datasets/semeion.py,,
torchvision/datasets/stanford_cars.py,,
torchvision/datasets/stl10.py,,
torchvision/datasets/sun397.py,,
torchvision/datasets/svhn.py,,
torchvision/datasets/ucf101.py,,
torchvision/datasets/usps.py,,
torchvision/datasets/utils.py,,
torchvision/datasets/video_utils.py,,
torchvision/datasets/vision.py,,
torchvision/datasets/voc.py,,
torchvision/datasets/widerface.py,,
torchvision/extension.py,,
torchvision/image.pyd,,
torchvision/io/__init__.py,,
torchvision/io/__pycache__/__init__.cpython-310.pyc,,
torchvision/io/__pycache__/_load_gpu_decoder.cpython-310.pyc,,
torchvision/io/__pycache__/_video_deprecation_warning.cpython-310.pyc,,
torchvision/io/__pycache__/_video_opt.cpython-310.pyc,,
torchvision/io/__pycache__/image.cpython-310.pyc,,
torchvision/io/__pycache__/video.cpython-310.pyc,,
torchvision/io/__pycache__/video_reader.cpython-310.pyc,,
torchvision/io/_load_gpu_decoder.py,,
torchvision/io/_video_deprecation_warning.py,,
torchvision/io/_video_opt.py,,
torchvision/io/image.py,,
torchvision/io/video.py,,
torchvision/io/video_reader.py,,
torchvision/jpeg8.dll,,
torchvision/libjpeg.dll,,
torchvision/libpng16.dll,,
torchvision/libsharpyuv.dll,,
torchvision/libwebp.dll,,
torchvision/models/__init__.py,,
torchvision/models/__pycache__/__init__.cpython-310.pyc,,
torchvision/models/__pycache__/_api.cpython-310.pyc,,
torchvision/models/__pycache__/_meta.cpython-310.pyc,,
torchvision/models/__pycache__/_utils.cpython-310.pyc,,
torchvision/models/__pycache__/alexnet.cpython-310.pyc,,
torchvision/models/__pycache__/convnext.cpython-310.pyc,,
torchvision/models/__pycache__/densenet.cpython-310.pyc,,
torchvision/models/__pycache__/efficientnet.cpython-310.pyc,,
torchvision/models/__pycache__/feature_extraction.cpython-310.pyc,,
torchvision/models/__pycache__/googlenet.cpython-310.pyc,,
torchvision/models/__pycache__/inception.cpython-310.pyc,,
torchvision/models/__pycache__/maxvit.cpython-310.pyc,,
torchvision/models/__pycache__/mnasnet.cpython-310.pyc,,
torchvision/models/__pycache__/mobilenet.cpython-310.pyc,,
torchvision/models/__pycache__/mobilenetv2.cpython-310.pyc,,
torchvision/models/__pycache__/mobilenetv3.cpython-310.pyc,,
torchvision/models/__pycache__/regnet.cpython-310.pyc,,
torchvision/models/__pycache__/resnet.cpython-310.pyc,,
torchvision/models/__pycache__/shufflenetv2.cpython-310.pyc,,
torchvision/models/__pycache__/squeezenet.cpython-310.pyc,,
torchvision/models/__pycache__/swin_transformer.cpython-310.pyc,,
torchvision/models/__pycache__/vgg.cpython-310.pyc,,
torchvision/models/__pycache__/vision_transformer.cpython-310.pyc,,
torchvision/models/_api.py,,
torchvision/models/_meta.py,,
torchvision/models/_utils.py,,
torchvision/models/alexnet.py,,
torchvision/models/convnext.py,,
torchvision/models/densenet.py,,
torchvision/models/detection/__init__.py,,
torchvision/models/detection/__pycache__/__init__.cpython-310.pyc,,
torchvision/models/detection/__pycache__/_utils.cpython-310.pyc,,
torchvision/models/detection/__pycache__/anchor_utils.cpython-310.pyc,,
torchvision/models/detection/__pycache__/backbone_utils.cpython-310.pyc,,
torchvision/models/detection/__pycache__/faster_rcnn.cpython-310.pyc,,
torchvision/models/detection/__pycache__/fcos.cpython-310.pyc,,
torchvision/models/detection/__pycache__/generalized_rcnn.cpython-310.pyc,,
torchvision/models/detection/__pycache__/image_list.cpython-310.pyc,,
torchvision/models/detection/__pycache__/keypoint_rcnn.cpython-310.pyc,,
torchvision/models/detection/__pycache__/mask_rcnn.cpython-310.pyc,,
torchvision/models/detection/__pycache__/retinanet.cpython-310.pyc,,
torchvision/models/detection/__pycache__/roi_heads.cpython-310.pyc,,
torchvision/models/detection/__pycache__/rpn.cpython-310.pyc,,
torchvision/models/detection/__pycache__/ssd.cpython-310.pyc,,
torchvision/models/detection/__pycache__/ssdlite.cpython-310.pyc,,
torchvision/models/detection/__pycache__/transform.cpython-310.pyc,,
torchvision/models/detection/_utils.py,,
torchvision/models/detection/anchor_utils.py,,
torchvision/models/detection/backbone_utils.py,,
torchvision/models/detection/faster_rcnn.py,,
torchvision/models/detection/fcos.py,,
torchvision/models/detection/generalized_rcnn.py,,
torchvision/models/detection/image_list.py,,
torchvision/models/detection/keypoint_rcnn.py,,
torchvision/models/detection/mask_rcnn.py,,
torchvision/models/detection/retinanet.py,,
torchvision/models/detection/roi_heads.py,,
torchvision/models/detection/rpn.py,,
torchvision/models/detection/ssd.py,,
torchvision/models/detection/ssdlite.py,,
torchvision/models/detection/transform.py,,
torchvision/models/efficientnet.py,,
torchvision/models/feature_extraction.py,,
torchvision/models/googlenet.py,,
torchvision/models/inception.py,,
torchvision/models/maxvit.py,,
torchvision/models/mnasnet.py,,
torchvision/models/mobilenet.py,,
torchvision/models/mobilenetv2.py,,
torchvision/models/mobilenetv3.py,,
torchvision/models/optical_flow/__init__.py,,
torchvision/models/optical_flow/__pycache__/__init__.cpython-310.pyc,,
torchvision/models/optical_flow/__pycache__/_utils.cpython-310.pyc,,
torchvision/models/optical_flow/__pycache__/raft.cpython-310.pyc,,
torchvision/models/optical_flow/_utils.py,,
torchvision/models/optical_flow/raft.py,,
torchvision/models/quantization/__init__.py,,
torchvision/models/quantization/__pycache__/__init__.cpython-310.pyc,,
torchvision/models/quantization/__pycache__/googlenet.cpython-310.pyc,,
torchvision/models/quantization/__pycache__/inception.cpython-310.pyc,,
torchvision/models/quantization/__pycache__/mobilenet.cpython-310.pyc,,
torchvision/models/quantization/__pycache__/mobilenetv2.cpython-310.pyc,,
torchvision/models/quantization/__pycache__/mobilenetv3.cpython-310.pyc,,
torchvision/models/quantization/__pycache__/resnet.cpython-310.pyc,,
torchvision/models/quantization/__pycache__/shufflenetv2.cpython-310.pyc,,
torchvision/models/quantization/__pycache__/utils.cpython-310.pyc,,
torchvision/models/quantization/googlenet.py,,
torchvision/models/quantization/inception.py,,
torchvision/models/quantization/mobilenet.py,,
torchvision/models/quantization/mobilenetv2.py,,
torchvision/models/quantization/mobilenetv3.py,,
torchvision/models/quantization/resnet.py,,
torchvision/models/quantization/shufflenetv2.py,,
torchvision/models/quantization/utils.py,,
torchvision/models/regnet.py,,
torchvision/models/resnet.py,,
torchvision/models/segmentation/__init__.py,,
torchvision/models/segmentation/__pycache__/__init__.cpython-310.pyc,,
torchvision/models/segmentation/__pycache__/_utils.cpython-310.pyc,,
torchvision/models/segmentation/__pycache__/deeplabv3.cpython-310.pyc,,
torchvision/models/segmentation/__pycache__/fcn.cpython-310.pyc,,
torchvision/models/segmentation/__pycache__/lraspp.cpython-310.pyc,,
torchvision/models/segmentation/_utils.py,,
torchvision/models/segmentation/deeplabv3.py,,
torchvision/models/segmentation/fcn.py,,
torchvision/models/segmentation/lraspp.py,,
torchvision/models/shufflenetv2.py,,
torchvision/models/squeezenet.py,,
torchvision/models/swin_transformer.py,,
torchvision/models/vgg.py,,
torchvision/models/video/__init__.py,,
torchvision/models/video/__pycache__/__init__.cpython-310.pyc,,
torchvision/models/video/__pycache__/mvit.cpython-310.pyc,,
torchvision/models/video/__pycache__/resnet.cpython-310.pyc,,
torchvision/models/video/__pycache__/s3d.cpython-310.pyc,,
torchvision/models/video/__pycache__/swin_transformer.cpython-310.pyc,,
torchvision/models/video/mvit.py,,
torchvision/models/video/resnet.py,,
torchvision/models/video/s3d.py,,
torchvision/models/video/swin_transformer.py,,
torchvision/models/vision_transformer.py,,
torchvision/nvjpeg64_11.dll,,
torchvision/ops/__init__.py,,
torchvision/ops/__pycache__/__init__.cpython-310.pyc,,
torchvision/ops/__pycache__/_box_convert.cpython-310.pyc,,
torchvision/ops/__pycache__/_register_onnx_ops.cpython-310.pyc,,
torchvision/ops/__pycache__/_utils.cpython-310.pyc,,
torchvision/ops/__pycache__/boxes.cpython-310.pyc,,
torchvision/ops/__pycache__/ciou_loss.cpython-310.pyc,,
torchvision/ops/__pycache__/deform_conv.cpython-310.pyc,,
torchvision/ops/__pycache__/diou_loss.cpython-310.pyc,,
torchvision/ops/__pycache__/drop_block.cpython-310.pyc,,
torchvision/ops/__pycache__/feature_pyramid_network.cpython-310.pyc,,
torchvision/ops/__pycache__/focal_loss.cpython-310.pyc,,
torchvision/ops/__pycache__/giou_loss.cpython-310.pyc,,
torchvision/ops/__pycache__/misc.cpython-310.pyc,,
torchvision/ops/__pycache__/poolers.cpython-310.pyc,,
torchvision/ops/__pycache__/ps_roi_align.cpython-310.pyc,,
torchvision/ops/__pycache__/ps_roi_pool.cpython-310.pyc,,
torchvision/ops/__pycache__/roi_align.cpython-310.pyc,,
torchvision/ops/__pycache__/roi_pool.cpython-310.pyc,,
torchvision/ops/__pycache__/stochastic_depth.cpython-310.pyc,,
torchvision/ops/_box_convert.py,,
torchvision/ops/_register_onnx_ops.py,,
torchvision/ops/_utils.py,,
torchvision/ops/boxes.py,,
torchvision/ops/ciou_loss.py,,
torchvision/ops/deform_conv.py,,
torchvision/ops/diou_loss.py,,
torchvision/ops/drop_block.py,,
torchvision/ops/feature_pyramid_network.py,,
torchvision/ops/focal_loss.py,,
torchvision/ops/giou_loss.py,,
torchvision/ops/misc.py,,
torchvision/ops/poolers.py,,
torchvision/ops/ps_roi_align.py,,
torchvision/ops/ps_roi_pool.py,,
torchvision/ops/roi_align.py,,
torchvision/ops/roi_pool.py,,
torchvision/ops/stochastic_depth.py,,
torchvision/prototype/__init__.py,,
torchvision/prototype/__pycache__/__init__.cpython-310.pyc,,
torchvision/prototype/datasets/__init__.py,,
torchvision/prototype/datasets/__pycache__/__init__.cpython-310.pyc,,
torchvision/prototype/datasets/__pycache__/_api.cpython-310.pyc,,
torchvision/prototype/datasets/__pycache__/_folder.cpython-310.pyc,,
torchvision/prototype/datasets/__pycache__/_home.cpython-310.pyc,,
torchvision/prototype/datasets/__pycache__/benchmark.cpython-310.pyc,,
torchvision/prototype/datasets/__pycache__/generate_category_files.cpython-310.pyc,,
torchvision/prototype/datasets/_api.py,,
torchvision/prototype/datasets/_builtin/__init__.py,,
torchvision/prototype/datasets/_builtin/__pycache__/__init__.cpython-310.pyc,,
torchvision/prototype/datasets/_builtin/__pycache__/caltech.cpython-310.pyc,,
torchvision/prototype/datasets/_builtin/__pycache__/celeba.cpython-310.pyc,,
torchvision/prototype/datasets/_builtin/__pycache__/cifar.cpython-310.pyc,,
torchvision/prototype/datasets/_builtin/__pycache__/clevr.cpython-310.pyc,,
torchvision/prototype/datasets/_builtin/__pycache__/coco.cpython-310.pyc,,
torchvision/prototype/datasets/_builtin/__pycache__/country211.cpython-310.pyc,,
torchvision/prototype/datasets/_builtin/__pycache__/cub200.cpython-310.pyc,,
torchvision/prototype/datasets/_builtin/__pycache__/dtd.cpython-310.pyc,,
torchvision/prototype/datasets/_builtin/__pycache__/eurosat.cpython-310.pyc,,
torchvision/prototype/datasets/_builtin/__pycache__/fer2013.cpython-310.pyc,,
torchvision/prototype/datasets/_builtin/__pycache__/food101.cpython-310.pyc,,
torchvision/prototype/datasets/_builtin/__pycache__/gtsrb.cpython-310.pyc,,
torchvision/prototype/datasets/_builtin/__pycache__/imagenet.cpython-310.pyc,,
torchvision/prototype/datasets/_builtin/__pycache__/mnist.cpython-310.pyc,,
torchvision/prototype/datasets/_builtin/__pycache__/oxford_iiit_pet.cpython-310.pyc,,
torchvision/prototype/datasets/_builtin/__pycache__/pcam.cpython-310.pyc,,
torchvision/prototype/datasets/_builtin/__pycache__/sbd.cpython-310.pyc,,
torchvision/prototype/datasets/_builtin/__pycache__/semeion.cpython-310.pyc,,
torchvision/prototype/datasets/_builtin/__pycache__/stanford_cars.cpython-310.pyc,,
torchvision/prototype/datasets/_builtin/__pycache__/svhn.cpython-310.pyc,,
torchvision/prototype/datasets/_builtin/__pycache__/usps.cpython-310.pyc,,
torchvision/prototype/datasets/_builtin/__pycache__/voc.cpython-310.pyc,,
torchvision/prototype/datasets/_builtin/caltech.py,,
torchvision/prototype/datasets/_builtin/caltech101.categories,,
torchvision/prototype/datasets/_builtin/caltech256.categories,,
torchvision/prototype/datasets/_builtin/celeba.py,,
torchvision/prototype/datasets/_builtin/cifar.py,,
torchvision/prototype/datasets/_builtin/cifar10.categories,,
torchvision/prototype/datasets/_builtin/cifar100.categories,,
torchvision/prototype/datasets/_builtin/clevr.py,,
torchvision/prototype/datasets/_builtin/coco.categories,,
torchvision/prototype/datasets/_builtin/coco.py,,
torchvision/prototype/datasets/_builtin/country211.categories,,
torchvision/prototype/datasets/_builtin/country211.py,,
torchvision/prototype/datasets/_builtin/cub200.categories,,
torchvision/prototype/datasets/_builtin/cub200.py,,
torchvision/prototype/datasets/_builtin/dtd.categories,,
torchvision/prototype/datasets/_builtin/dtd.py,,
torchvision/prototype/datasets/_builtin/eurosat.py,,
torchvision/prototype/datasets/_builtin/fer2013.py,,
torchvision/prototype/datasets/_builtin/food101.categories,,
torchvision/prototype/datasets/_builtin/food101.py,,
torchvision/prototype/datasets/_builtin/gtsrb.py,,
torchvision/prototype/datasets/_builtin/imagenet.categories,,
torchvision/prototype/datasets/_builtin/imagenet.py,,
torchvision/prototype/datasets/_builtin/mnist.py,,
torchvision/prototype/datasets/_builtin/oxford-iiit-pet.categories,,
torchvision/prototype/datasets/_builtin/oxford_iiit_pet.py,,
torchvision/prototype/datasets/_builtin/pcam.py,,
torchvision/prototype/datasets/_builtin/sbd.categories,,
torchvision/prototype/datasets/_builtin/sbd.py,,
torchvision/prototype/datasets/_builtin/semeion.py,,
torchvision/prototype/datasets/_builtin/stanford-cars.categories,,
torchvision/prototype/datasets/_builtin/stanford_cars.py,,
torchvision/prototype/datasets/_builtin/svhn.py,,
torchvision/prototype/datasets/_builtin/usps.py,,
torchvision/prototype/datasets/_builtin/voc.categories,,
torchvision/prototype/datasets/_builtin/voc.py,,
torchvision/prototype/datasets/_folder.py,,
torchvision/prototype/datasets/_home.py,,
torchvision/prototype/datasets/benchmark.py,,
torchvision/prototype/datasets/generate_category_files.py,,
torchvision/prototype/datasets/utils/__init__.py,,
torchvision/prototype/datasets/utils/__pycache__/__init__.cpython-310.pyc,,
torchvision/prototype/datasets/utils/__pycache__/_dataset.cpython-310.pyc,,
torchvision/prototype/datasets/utils/__pycache__/_encoded.cpython-310.pyc,,
torchvision/prototype/datasets/utils/__pycache__/_internal.cpython-310.pyc,,
torchvision/prototype/datasets/utils/__pycache__/_resource.cpython-310.pyc,,
torchvision/prototype/datasets/utils/_dataset.py,,
torchvision/prototype/datasets/utils/_encoded.py,,
torchvision/prototype/datasets/utils/_internal.py,,
torchvision/prototype/datasets/utils/_resource.py,,
torchvision/prototype/models/__init__.py,,
torchvision/prototype/models/__pycache__/__init__.cpython-310.pyc,,
torchvision/prototype/models/depth/__init__.py,,
torchvision/prototype/models/depth/__pycache__/__init__.cpython-310.pyc,,
torchvision/prototype/models/depth/stereo/__init__.py,,
torchvision/prototype/models/depth/stereo/__pycache__/__init__.cpython-310.pyc,,
torchvision/prototype/models/depth/stereo/__pycache__/crestereo.cpython-310.pyc,,
torchvision/prototype/models/depth/stereo/__pycache__/raft_stereo.cpython-310.pyc,,
torchvision/prototype/models/depth/stereo/crestereo.py,,
torchvision/prototype/models/depth/stereo/raft_stereo.py,,
torchvision/prototype/transforms/__init__.py,,
torchvision/prototype/transforms/__pycache__/__init__.cpython-310.pyc,,
torchvision/prototype/transforms/__pycache__/_augment.cpython-310.pyc,,
torchvision/prototype/transforms/__pycache__/_geometry.cpython-310.pyc,,
torchvision/prototype/transforms/__pycache__/_misc.cpython-310.pyc,,
torchvision/prototype/transforms/__pycache__/_presets.cpython-310.pyc,,
torchvision/prototype/transforms/__pycache__/_type_conversion.cpython-310.pyc,,
torchvision/prototype/transforms/_augment.py,,
torchvision/prototype/transforms/_geometry.py,,
torchvision/prototype/transforms/_misc.py,,
torchvision/prototype/transforms/_presets.py,,
torchvision/prototype/transforms/_type_conversion.py,,
torchvision/prototype/tv_tensors/__init__.py,,
torchvision/prototype/tv_tensors/__pycache__/__init__.cpython-310.pyc,,
torchvision/prototype/tv_tensors/__pycache__/_label.cpython-310.pyc,,
torchvision/prototype/tv_tensors/_label.py,,
torchvision/prototype/utils/__init__.py,,
torchvision/prototype/utils/__pycache__/__init__.cpython-310.pyc,,
torchvision/prototype/utils/__pycache__/_internal.cpython-310.pyc,,
torchvision/prototype/utils/_internal.py,,
torchvision/transforms/__init__.py,,
torchvision/transforms/__pycache__/__init__.cpython-310.pyc,,
torchvision/transforms/__pycache__/_functional_pil.cpython-310.pyc,,
torchvision/transforms/__pycache__/_functional_tensor.cpython-310.pyc,,
torchvision/transforms/__pycache__/_functional_video.cpython-310.pyc,,
torchvision/transforms/__pycache__/_presets.cpython-310.pyc,,
torchvision/transforms/__pycache__/_transforms_video.cpython-310.pyc,,
torchvision/transforms/__pycache__/autoaugment.cpython-310.pyc,,
torchvision/transforms/__pycache__/functional.cpython-310.pyc,,
torchvision/transforms/__pycache__/transforms.cpython-310.pyc,,
torchvision/transforms/_functional_pil.py,,
torchvision/transforms/_functional_tensor.py,,
torchvision/transforms/_functional_video.py,,
torchvision/transforms/_presets.py,,
torchvision/transforms/_transforms_video.py,,
torchvision/transforms/autoaugment.py,,
torchvision/transforms/functional.py,,
torchvision/transforms/transforms.py,,
torchvision/transforms/v2/__init__.py,,
torchvision/transforms/v2/__pycache__/__init__.cpython-310.pyc,,
torchvision/transforms/v2/__pycache__/_augment.cpython-310.pyc,,
torchvision/transforms/v2/__pycache__/_auto_augment.cpython-310.pyc,,
torchvision/transforms/v2/__pycache__/_color.cpython-310.pyc,,
torchvision/transforms/v2/__pycache__/_container.cpython-310.pyc,,
torchvision/transforms/v2/__pycache__/_deprecated.cpython-310.pyc,,
torchvision/transforms/v2/__pycache__/_geometry.cpython-310.pyc,,
torchvision/transforms/v2/__pycache__/_meta.cpython-310.pyc,,
torchvision/transforms/v2/__pycache__/_misc.cpython-310.pyc,,
torchvision/transforms/v2/__pycache__/_temporal.cpython-310.pyc,,
torchvision/transforms/v2/__pycache__/_transform.cpython-310.pyc,,
torchvision/transforms/v2/__pycache__/_type_conversion.cpython-310.pyc,,
torchvision/transforms/v2/__pycache__/_utils.cpython-310.pyc,,
torchvision/transforms/v2/_augment.py,,
torchvision/transforms/v2/_auto_augment.py,,
torchvision/transforms/v2/_color.py,,
torchvision/transforms/v2/_container.py,,
torchvision/transforms/v2/_deprecated.py,,
torchvision/transforms/v2/_geometry.py,,
torchvision/transforms/v2/_meta.py,,
torchvision/transforms/v2/_misc.py,,
torchvision/transforms/v2/_temporal.py,,
torchvision/transforms/v2/_transform.py,,
torchvision/transforms/v2/_type_conversion.py,,
torchvision/transforms/v2/_utils.py,,
torchvision/transforms/v2/functional/__init__.py,,
torchvision/transforms/v2/functional/__pycache__/__init__.cpython-310.pyc,,
torchvision/transforms/v2/functional/__pycache__/_augment.cpython-310.pyc,,
torchvision/transforms/v2/functional/__pycache__/_color.cpython-310.pyc,,
torchvision/transforms/v2/functional/__pycache__/_deprecated.cpython-310.pyc,,
torchvision/transforms/v2/functional/__pycache__/_geometry.cpython-310.pyc,,
torchvision/transforms/v2/functional/__pycache__/_meta.cpython-310.pyc,,
torchvision/transforms/v2/functional/__pycache__/_misc.cpython-310.pyc,,
torchvision/transforms/v2/functional/__pycache__/_temporal.cpython-310.pyc,,
torchvision/transforms/v2/functional/__pycache__/_type_conversion.cpython-310.pyc,,
torchvision/transforms/v2/functional/__pycache__/_utils.cpython-310.pyc,,
torchvision/transforms/v2/functional/_augment.py,,
torchvision/transforms/v2/functional/_color.py,,
torchvision/transforms/v2/functional/_deprecated.py,,
torchvision/transforms/v2/functional/_geometry.py,,
torchvision/transforms/v2/functional/_meta.py,,
torchvision/transforms/v2/functional/_misc.py,,
torchvision/transforms/v2/functional/_temporal.py,,
torchvision/transforms/v2/functional/_type_conversion.py,,
torchvision/transforms/v2/functional/_utils.py,,
torchvision/tv_tensors/__init__.py,,
torchvision/tv_tensors/__pycache__/__init__.cpython-310.pyc,,
torchvision/tv_tensors/__pycache__/_bounding_boxes.cpython-310.pyc,,
torchvision/tv_tensors/__pycache__/_dataset_wrapper.cpython-310.pyc,,
torchvision/tv_tensors/__pycache__/_image.cpython-310.pyc,,
torchvision/tv_tensors/__pycache__/_mask.cpython-310.pyc,,
torchvision/tv_tensors/__pycache__/_torch_function_helpers.cpython-310.pyc,,
torchvision/tv_tensors/__pycache__/_tv_tensor.cpython-310.pyc,,
torchvision/tv_tensors/__pycache__/_video.cpython-310.pyc,,
torchvision/tv_tensors/_bounding_boxes.py,,
torchvision/tv_tensors/_dataset_wrapper.py,,
torchvision/tv_tensors/_image.py,,
torchvision/tv_tensors/_mask.py,,
torchvision/tv_tensors/_torch_function_helpers.py,,
torchvision/tv_tensors/_tv_tensor.py,,
torchvision/tv_tensors/_video.py,,
torchvision/utils.py,,
torchvision/version.py,,
torchvision/zlib.dll,,
torchvision\_C.pyd,sha256=uwKoLN_f4Ige1CZEvBfY8SQK9Jnth3emUr21YukmBTs,6133248
torchvision\__init__.py,sha256=yUybb2eKRBX2LWsDZPAnEpHWtEFcGHS-Xo5r8K-Ric0,3639
torchvision\_internally_replaced_utils.py,sha256=LogrBRyA6oM0joW1_kzgVYicV6ZYCFsszHbcuPxgGSA,1458
torchvision\_meta_registrations.py,sha256=_rNIAEDFkHMmgYGm982-WgSmhA9o2KVJpd8DsoUlQmk,7433
torchvision\_utils.py,sha256=kcSn6P3Vjv5QPgHHNmNu59Mh-NLb5MDlYxvDkNJWrOM,966
torchvision\cudart64_110.dll,sha256=ulwvtSbE7kuyGM6z-l6L_eic5HTzhxH9zOgCVJv5_G8,526848
torchvision\datasets\__init__.py,sha256=Ajw_RJcPNYz-iKvkvQEkgw2P341llDsDgd8cke-bxas,3753
torchvision\datasets\_optical_flow.py,sha256=8piiPLeuH8SMi-Pwz8C9UOWEbnhnPVxytvDXE4OjKck,21733
torchvision\datasets\_stereo_matching.py,sha256=z-wTMLgVQZlTfeL9POzk9xbbR-xwcEJqYiYCePqT6Rs,50281
torchvision\datasets\caltech.py,sha256=HwTa-G4rGIzyXXsV9U5cFQoCp-f5BETeP8l078S9d2A,9055
torchvision\datasets\celeba.py,sha256=TergGWKRU6f4KUuHCOxrVot2ktMaXSrTyaDJwgNBIm8,8758
torchvision\datasets\cifar.py,sha256=SrmM2jGfT3jRG8rPj9E6mjT89TgZZu6FLG8JU2DXmWc,5958
torchvision\datasets\cityscapes.py,sha256=UXjwN_IEoNoNLUkWjVAIw7-a87ew1lhK6aVvFx0_IXU,10571
torchvision\datasets\clevr.py,sha256=ur8AdpoSqzIip1iOTnY-1eWNzS-EC77pLVp-2CDGpHI,3961
torchvision\datasets\coco.py,sha256=6m8E4urLnNkreVZNjNn2r3au4NSpeOEomd4bl-NIEKo,4469
torchvision\datasets\country211.py,sha256=aw2FzdX-OTQtvHFldL7p5CVA02-7Qt6AXX6j4UQEC8M,2956
torchvision\datasets\dtd.py,sha256=pgvCnfobDwjpxOsvAxYFtwbbsmRNVnMdX2KC-1W1WSQ,4532
torchvision\datasets\eurosat.py,sha256=vLcnf6XlVIT9MG1ZTPB8kfY10TXqTJcZ6QelpQNmqSE,2832
torchvision\datasets\fakedata.py,sha256=BqGViPyIRcvdBNpGhsPl_isrxxOTtlFLyKnLxzBWQP8,2514
torchvision\datasets\fer2013.py,sha256=d1cRDnnrPP8M-DxeguCLnF6vmQMO2QC9LX6Zk8pPfrE,5238
torchvision\datasets\fgvc_aircraft.py,sha256=9o3Lg18EhkxTsmtdWFPeNIZJVgRrlTmQX4XfognSAa4,5134
torchvision\datasets\flickr.py,sha256=ewZbPFFmqrzoLgxHMWxE3wC8m1ntEMbVYy5GH1eM_Ok,6365
torchvision\datasets\flowers102.py,sha256=RGfwpUmh-cW_3rVAfF8X58MctlnbqWaMKWAvxj6f7Vw,7713
torchvision\datasets\folder.py,sha256=QDKCSTU-rFYmmdJjii8Xu1_QkpET41gutgMxsbcbBJI,13341
torchvision\datasets\food101.py,sha256=WIltYYDL9pKDiNLbmGXoRtx0qfQDR6L993uSMIM_Wgg,4250
torchvision\datasets\gtsrb.py,sha256=TVMesJ3jzVk1tg4ecJyiun5lQ0PKh7lMZWLkZbkVbUY,3888
torchvision\datasets\hmdb51.py,sha256=nAfL2TmZMNAuxv6hN1X3jNP-DmBEF5ls5YnjHo64TIs,6123
torchvision\datasets\imagenet.py,sha256=EfCXtiVzvSNaoi2ZwSn9sfkcWP1BRXHGfjL3cCM_Qt4,9135
torchvision\datasets\imagenette.py,sha256=JGTamZZIfXqVxNdhPC54NzddfdrUAf6k7-29Y7rNq3w,4737
torchvision\datasets\inaturalist.py,sha256=JBIEXSa32cog3QMvXwAzUDWo1I-fz1pRZuIH3SxkXPM,10492
torchvision\datasets\kinetics.py,sha256=7aRbG-PXkhm9jahFHq6aIyw-QKdyUJqVdvV7UjmT1_k,10115
torchvision\datasets\kitti.py,sha256=q0otSanUygTFq4P2T3xKft1Gv-BPQXw-xNNMfOmDOzc,5795
torchvision\datasets\lfw.py,sha256=xoW_cunDI4esj83cMxdu84VLJDxVFFQyH6V9_TPzSTc,11396
torchvision\datasets\lsun.py,sha256=TomHtE2AGd3TISKGcMuGvJXSeVtH6x6nNq-5B-1kl0M,5911
torchvision\datasets\mnist.py,sha256=6crNP4vOgo4u6-Moq2ylgBncHs9WDKVrwkooo2LBRtU,22366
torchvision\datasets\moving_mnist.py,sha256=Cmw-Pj4xEzmfGZab5O0SGHjxYntg7lyL6XCwuzr1FTQ,3738
torchvision\datasets\omniglot.py,sha256=q01sKLB66_QDU80_6oLqT1p66S9oaxsBNojyX2StUsY,4606
torchvision\datasets\oxford_iiit_pet.py,sha256=C9o0Iq1c6HLqmiGehcW8TJoEvliXx3GL7-yQQXmd_30,5810
torchvision\datasets\pcam.py,sha256=8tWbJcxo7lKYTToISFpL6_OSbxeYEqU2QsaY6OYjRpM,5419
torchvision\datasets\phototour.py,sha256=DI7oqvaijkoWMpogfvkK-L5QAY8-H1bvN9IBVYR25zA,8098
torchvision\datasets\places365.py,sha256=aJwZuA5bCFvyziu2NzFXATA19k6-qwmvJHZOE4deb08,7662
torchvision\datasets\rendered_sst2.py,sha256=_NxSnOzH3rVguwMpK_gra5UZhQpy4g37UH89ZB4HF8o,4053
torchvision\datasets\samplers\__init__.py,sha256=yX8XD3h-VwTCoqF9IdQmcuGblZxvXb2EIqc5lPqXXtY,164
torchvision\datasets\samplers\clip_sampler.py,sha256=ERp9c0O3ZGQNR3lLXvZXWjfJkXayYym8bhYfOm_MNKI,6416
torchvision\datasets\sbd.py,sha256=aSgEpJSgnh9uUp6xEGXTGRIERSY6hdT5RWpw3dnCap0,5540
torchvision\datasets\sbu.py,sha256=n743sOTC9nRHcaLwpzyWW2KB24qzbGE_stKBp5FYcPg,4585
torchvision\datasets\semeion.py,sha256=6TvuVqBRvGPXj7SGPm3Q2QnnFieH8d7gOe-OMskFydg,3180
torchvision\datasets\stanford_cars.py,sha256=aJrRR91UJbdpvkx_vMU3h8rasqv_Msy7gXXacQ4CMlg,4393
torchvision\datasets\stl10.py,sha256=dGiWMjM2cLaAcj5iQ7oJUVv43IL8SUNKX0Up6HRj-8c,7408
torchvision\datasets\sun397.py,sha256=0eJtaRqrHzko27q-J1-UgQgRT8FDe8wpen68ahmE6ds,3264
torchvision\datasets\svhn.py,sha256=nP-62KxLkLD6n84lw5lgyblHyRFWC5NYiBLoGgeID5Y,4958
torchvision\datasets\ucf101.py,sha256=zM9EY6Clqdvt45PZyL0dZvnlLn-5oyCD6v924bENlPE,5664
torchvision\datasets\usps.py,sha256=vaztMPU18onO_7tWXUWNQ0QsPPYoxGB-3a7YiA3pYrs,3596
torchvision\datasets\utils.py,sha256=CMvpluClWFS6KIPczX18ruPwGC-5q7uNAC75H8ak6xk,16465
torchvision\datasets\video_utils.py,sha256=gUazc9gv4wgFuJ1N47e2c4Wi86HWhR9O09HcwVraeLA,17632
torchvision\datasets\vision.py,sha256=5N69xiFsNdiqjVzP-QP3-PtswDUNsKSeyy_FzXTDt_4,4360
torchvision\datasets\voc.py,sha256=7GhzVyU3iWbBzFyb1zdj-9xxCSLmQCdihPO_o85SdqA,9059
torchvision\datasets\widerface.py,sha256=MU382b0UIeX3BaBtwin5BKWoLnTXOPhIR89vrNmLrsU,8460
torchvision\extension.py,sha256=0A4efQ6V8RlQcMMtDpK74VIBHpDv4icjkkOc-EokPHw,3233
torchvision\image.pyd,sha256=FX_9KibU8PT1mbLT2VaqhnhwFt6JPfrEDXe_xyYgY3A,202752
torchvision\io\__init__.py,sha256=gzG3Qr6eYLnwemcJFTmWwL8g4Bfd3K9BRVydqo2EnY4,1656
torchvision\io\_load_gpu_decoder.py,sha256=vUWhEXG1Dq3pIJHd8avo4o333Z7zx2DN7FOzIxuGluE,186
torchvision\io\_video_deprecation_warning.py,sha256=iQ5WX0Kz-URtrFG5tqc04KSspCHZYUGUDwr3lx0y27s,462
torchvision\io\_video_opt.py,sha256=e8Qfas1Dfeu3KpiGfoCStyFQBYB5m5U9x66ie-YqNfM,21323
torchvision\io\image.py,sha256=2J0_nN-ZICmw2p8jMPMgQtYM1AOxcksyrD_zPUsyqnA,22229
torchvision\io\video.py,sha256=cSHd6k8vfKrsIcJ-1jZqMhMOgcg_HngLP-xDk35MI38,18820
torchvision\io\video_reader.py,sha256=7cMi2HKfWJ6vLwZ-IirLzh8gVACBzisn0N4cKSxgZ4c,12144
torchvision\jpeg8.dll,sha256=aM-Kj2MkrdHI0gkgpHfh86_icuM26XiMu6gyMGeuKig,552448
torchvision\libjpeg.dll,sha256=85Tk07ezKBsmEjt5H6Yu53aO-ckgPdT6CxG6C-VYyx8,284944
torchvision\libpng16.dll,sha256=nPxu7uIrOxgpEXCLUyjP7rQBCghBVPsL-h8OxeArvc0,192512
torchvision\libsharpyuv.dll,sha256=6Lz4Enzhcx9UiWUBCycwqTHTi_D-35YOM-p8zbwB2QM,34576
torchvision\libwebp.dll,sha256=hN1hG6LP-7hh6DCpbYtX82QTWG0KT2EFtSaX2wrJqOc,389392
torchvision\models\__init__.py,sha256=6QlTJfvjKcUmMJvwSapWUNFXbf2Vo15dVRcBuNSaYko,888
torchvision\models\_api.py,sha256=l4hKX0GCJpC6fHK0QQmHDHnWg4VfmuuskGXShHzQ2lU,10248
torchvision\models\_meta.py,sha256=2NSIICoq4MDzPZc00DlGJTgHOCwTBSObSTeRTh3E0tQ,30429
torchvision\models\_utils.py,sha256=X7zduE90fkek8DjukzyENOcZ0iop03R0LIxC_FuAazk,11149
torchvision\models\alexnet.py,sha256=XcldP2UuOkdOUfdxitGS8qHzLH78Ny7VCzTzKsaWITU,4607
torchvision\models\convnext.py,sha256=mML3XALGvQGe3aYkmz6dbC7Wus7ntZWr7IVihWjLV8M,15740
torchvision\models\densenet.py,sha256=Wxvecj8b09Uy2FjKun7ZbagpDmHll8L2c3GbM802Ivs,17273
torchvision\models\detection\__init__.py,sha256=D4cs338Z4BQn5TgX2IKuJC9TD2rtw2svUDZlALR-lwI,175
torchvision\models\detection\_utils.py,sha256=m9bowqjuYiR9A7HI7wJAK_kgFrUYlKSukXOEwuUtRpA,22667
torchvision\models\detection\anchor_utils.py,sha256=TQKWOKDFALsTmYw_BMGIDlT9mNQhukmgnNdFuRjN49w,12127
torchvision\models\detection\backbone_utils.py,sha256=BBKVxCyAY9Q8JYOLQ3oAbWJxcjn5HAbPeAcDb-5JoVA,10792
torchvision\models\detection\faster_rcnn.py,sha256=rhMj68KQ4uDeFKT30PVy2xlqLG43u6tA7i9e4MEM15U,37825
torchvision\models\detection\fcos.py,sha256=3qi44CnUveI7W3Uu7u39fg7H5-2JT1C97dEgdSORLog,35010
torchvision\models\detection\generalized_rcnn.py,sha256=nLVj2o8yr6BW1MN12u30QuFbvtsdllEbUlbNH-dO-G0,4861
torchvision\models\detection\image_list.py,sha256=IzFjxIaMdyFas1IHPBgAuBK3iYJOert5HzGurYJitNk,808
torchvision\models\detection\keypoint_rcnn.py,sha256=gzdI2mw2ububcgBhO8U6u17U_hgAEIADtr45aT5iPt4,22421
torchvision\models\detection\mask_rcnn.py,sha256=HZyuWNdOw8WIdO05-8obUED1A0CVuFd5E21KCzzCfFE,27303
torchvision\models\detection\retinanet.py,sha256=aWY__9rz8r0bT2Lvl0P5llW2hE42s6vNOljaTF4hqOU,38203
torchvision\models\detection\roi_heads.py,sha256=f_Lde69JHugGAaiGWh6ugJ9WUTT8f7InxPry_MZxgZY,34698
torchvision\models\detection\rpn.py,sha256=z4ezvg1XS9uNq_3jIYXzIRnsAhuGClZ5AJ5P0NDziN8,16226
torchvision\models\detection\ssd.py,sha256=QyWW7IihpNG4_SWeVo1-X5J9kDJrasd794-AHIcDxQY,29661
torchvision\models\detection\ssdlite.py,sha256=DRkN07LP7WTKt6Q5WIOraQR37Unx7XIiB70UdhmDCcE,13550
torchvision\models\detection\transform.py,sha256=gou0mGGPzLncV2ZMHkTTkw6UO0fUoJii6Dr2N0PNA_Y,12508
torchvision\models\efficientnet.py,sha256=tW6BpsBProhN6b1o1_sHSikSoqDupQRgdgRETiYjcAY,44221
torchvision\models\feature_extraction.py,sha256=mHanRc9yimz90mj9y2rEBKmd0lL5ayfsAP8VpgDln00,28540
torchvision\models\googlenet.py,sha256=oxCJp5dklEoEWu6VwHSgX-pifaOhZ4gH9bz71P-hE-o,13151
torchvision\models\inception.py,sha256=l9tutwO7KNVa5nfdl1_5f-6lJzQ-NSOCzXPArDILeAA,19329
torchvision\models\maxvit.py,sha256=vHRyAGJ8FsPX_bYZWmmm-6mG6NUq3cICHj00rLd8vow,32929
torchvision\models\mnasnet.py,sha256=PTSF4DchbFgtOchd9kgoPKCcfKH6NC4WfHa5bv4jZ58,18008
torchvision\models\mobilenet.py,sha256=alrEJwktmXVcCphU8h2EAJZX0YdKfcz4tJEOdG2BXB8,217
torchvision\models\mobilenetv2.py,sha256=eVil23yP4f-DBUhjKt73mao84zEiF5Y01wHNwh8HCdM,9970
torchvision\models\mobilenetv3.py,sha256=g1Ul1RkohPHhXfsecg2r0W7NVlEgVUc7X_uT1bxxrTQ,16702
torchvision\models\optical_flow\__init__.py,sha256=uuRFAdvcDobdAbY2VmxEZ7_CLH_f5-JRkCSuJRkj4RY,21
torchvision\models\optical_flow\_utils.py,sha256=PRcuU-IB6EL3hAOLiyC5q-NBzlvIKfhSF_BMplHbzfY,2125
torchvision\models\optical_flow\raft.py,sha256=V1wMHZSDgg8yZBpHwVaVcA-fd8FQt6VUud93sbPg9NA,40944
torchvision\models\quantization\__init__.py,sha256=YOJmYqWQTfP5P2ypteZNKQOMW4VEB2WHJlYoSlSaL1Y,130
torchvision\models\quantization\googlenet.py,sha256=P92cacoKVTV4cDoaNkRevLx1daGH5DkPfUwPDMuOXO0,8290
torchvision\models\quantization\inception.py,sha256=TXz2hRpSvh6zYP398MsXTYQMnqYgnYnq5wyG6xr5Nhk,11088
torchvision\models\quantization\mobilenet.py,sha256=alrEJwktmXVcCphU8h2EAJZX0YdKfcz4tJEOdG2BXB8,217
torchvision\models\quantization\mobilenetv2.py,sha256=g2z3HPQ0MXFCuMV5TpLPRdO99m3wC_3d0ukUanXaJHo,6037
torchvision\models\quantization\mobilenetv3.py,sha256=l9g1AwKiU35XKuobXo-UPe5MqRKC1kgN7xgWWim4qr4,9467
torchvision\models\quantization\resnet.py,sha256=azvn1vwebP22qryYGNgFLMviGjHklXOX7xd4C2cggUo,18423
torchvision\models\quantization\shufflenetv2.py,sha256=7k9MLRLzjP3vke-e0Ai_cnA-j15KGQq5yDcs_ELXUg8,17311
torchvision\models\quantization\utils.py,sha256=Ij88l6toyO8MQi1w512Jt-yQ2Q9hK75-Z2SOjIzS6Zw,2109
torchvision\models\regnet.py,sha256=NbsA3RO7ka7k9fwYyVZ5wvvtJUuhXqyX76aGIoIujGE,65124
torchvision\models\resnet.py,sha256=AXWWl7XlkSQpUCsv8lCaWeDuYaq-KyOLXmBe0R8rv58,39917
torchvision\models\segmentation\__init__.py,sha256=TLL2SSmqE08HLiv_yyIWyIyrvf2xaOsZi0muDv_Y5Vc,69
torchvision\models\segmentation\_utils.py,sha256=yFeyBa5_Pyv1UQ_2N64XMRgTYsxifwzDd-VRP-vmIGM,1234
torchvision\models\segmentation\deeplabv3.py,sha256=MkmYEm1dF4afQEYXmFfxLAcqioiT5uWKYGSXCccIYh4,15405
torchvision\models\segmentation\fcn.py,sha256=mQ1Wi4S9j5G6OQbNciuxNwVbJ6e9miTzIWj6mUF5JwA,9205
torchvision\models\segmentation\lraspp.py,sha256=yx_b3PJsH5e0F3TqiGDhEnbXCGTdNX2iIxsKvNenM0s,7821
torchvision\models\shufflenetv2.py,sha256=VEGsTNNTdqdu8m7I62zQuJK_5CkET-0Y4ixYBJ-QBCs,15852
torchvision\models\squeezenet.py,sha256=Dha-ci350KU15D0LS9N07kw6MlNuusUHSBnC83Ery_E,8986
torchvision\models\swin_transformer.py,sha256=-Q9Kd1qzsD7vL3u137Q4MoHSTwzA6QFcweaF0zCWmUk,40370
torchvision\models\vgg.py,sha256=Qt9r5sFoY-oPdwP4De61jqSVe9XUZLXK47r9yVDQ33M,19736
torchvision\models\video\__init__.py,sha256=xHHR5c6kP0cMDXck7XnXq19iJL_Uemcxg3OC00cqE6A,97
torchvision\models\video\mvit.py,sha256=xIK4nCOuJWXQjoX8NzcouwzyTkIkcFug3yiu0a5-Dk8,33895
torchvision\models\video\resnet.py,sha256=JOP7FDfUOfQQP-jEYUvzSIOr9Iaexl2L3iUh-rzrp90,17274
torchvision\models\video\s3d.py,sha256=Rn-iypP13jrETAap1Qd4NY6kkpYDuSXjGkEKZDOxemI,8034
torchvision\models\video\swin_transformer.py,sha256=M74P2v4lVKM6zgwoeFn_njppZB2l-gAjuGVvzzESKpU,28431
torchvision\models\vision_transformer.py,sha256=GE-_-dlFJQPTnONe4qrzYOYp-wavPOrFPCo9krM39Vg,33000
torchvision\nvjpeg64_11.dll,sha256=gVFLDPTckuScHJN99rGS99ttPYU5hldp1Q2TJq2-Nz8,3862528
torchvision\ops\__init__.py,sha256=7wibGxcF1JHDviSNs9O9Pwlc8dhMSFfZo0wzVjTFnAY,2001
torchvision\ops\_box_convert.py,sha256=glF6sulLzaw_KG36wg0CHWt0ef62BnkjokbqQnBUMsU,2490
torchvision\ops\_register_onnx_ops.py,sha256=g4M5Fp7n_5ZTzIQcUXvEct3YFlUMPNVSQQfBP-J0eQQ,4288
torchvision\ops\_utils.py,sha256=xFrLnLhKDHiG2TN39tUWY-MJbLEPka6dkaVVJFAN7-8,3736
torchvision\ops\boxes.py,sha256=oz84ziFAWrariAzwUDM31tQUIHPm7yFrxk1yHi8QJW4,17101
torchvision\ops\ciou_loss.py,sha256=Qzm89C82ehX-YvYBPLPRPhbJZdr3itizxuxrT7MLi9o,2834
torchvision\ops\deform_conv.py,sha256=DuIosFDK3tsY5RlHU7mez5x1p08IQai9WG14z3S0gzU,7185
torchvision\ops\diou_loss.py,sha256=6IebWlMYc_2YnbG36niDXgM16vxajSKRfiusEuUJwpQ,3456
torchvision\ops\drop_block.py,sha256=ZkIzM1b3v5_U7z0eavzaNpN7IBq0N4ZNwwvWArispwg,6010
torchvision\ops\feature_pyramid_network.py,sha256=Jts5mzUJX3EarcAQU5MDUe0a5Sgn5YjUstaW2JQpgEE,8952
torchvision\ops\focal_loss.py,sha256=A-Ec5GG7sbyE8ydGP6QuAPdtkUbDfdg5j4zYvs5PwzA,2480
torchvision\ops\giou_loss.py,sha256=xB_RlES28k_A6iH2VqWAPBQkiT_zkEwdtREDGR_nVJM,2772
torchvision\ops\misc.py,sha256=niQnKPuifQzVXAWnnf6TkVsgetqyetjZ6lq3wi2tsZw,13892
torchvision\ops\poolers.py,sha256=sfgcZWh2dIo9UY437CnpAHdxqPQhuvjNPYzhIKlAIPE,12247
torchvision\ops\ps_roi_align.py,sha256=6_kmnE6z_3AZZ1N2hrS_uK3cbuzqZhjdM2rC50mfYUo,3715
torchvision\ops\ps_roi_pool.py,sha256=2JrjJwzVtEeEg0BebkCnGfq4xEDwMCD-Xh915mvNcyI,2940
torchvision\ops\roi_align.py,sha256=CLImbp3E5mGHDI6nDy-1jqS2ESW2sy57MxEspqSHxPo,11614
torchvision\ops\roi_pool.py,sha256=cN7rSCQfpUzETvP8SjPDl1yfXjbxg9I-tXnHbvAKya8,3015
torchvision\ops\stochastic_depth.py,sha256=9T4Zu_BaemKZafSmRwrPCVr5aaGH8tmzlsQAZO-1_-Y,2302
torchvision\prototype\__init__.py,sha256=TE6LTOe1Woda1jAUs5QZUN91JZpmpHvDSBgtEW-X2_U,53
torchvision\prototype\datasets\__init__.py,sha256=AhYIQPi2NKyYAXPNRdSUPqQgbLxXRERh52sV60UFm_A,647
torchvision\prototype\datasets\_api.py,sha256=hPbErbWvryAg-PLjB-O14Q9Poa1aPPUj8ogGmW5BzI0,1828
torchvision\prototype\datasets\_builtin\__init__.py,sha256=NYHJ0f09xIso6BwzIypEVzyESHPK8bn9YcaZ8Qs9CAc,690
torchvision\prototype\datasets\_builtin\caltech.py,sha256=kAGIRPaY36yW9wCruQmZaqPBO3_mK10cwjpy_itrPsU,7079
torchvision\prototype\datasets\_builtin\caltech101.categories,sha256=uC6hl4Cy3lOt_ZQhvB6FVi3G3eg5tk72ftdb8ST_Yqc,989
torchvision\prototype\datasets\_builtin\caltech256.categories,sha256=r4KdwVr7FZuvuTUSyck4kIYHVTnceEbT8iUFlxpn5dA,2878
torchvision\prototype\datasets\_builtin\celeba.py,sha256=B-r2eSv54_BU4z_nZB7rG_nyadk-tIMKP7sTPOckgDs,7332
torchvision\prototype\datasets\_builtin\cifar.py,sha256=zfYe20DZb-_mC7BxZo1l9OajPefyLd6un8K8pqzMOfY,4736
torchvision\prototype\datasets\_builtin\cifar10.categories,sha256=633jy9l275PzAFvCiq5iBseI102D2cYeslJqYabu72s,70
torchvision\prototype\datasets\_builtin\cifar100.categories,sha256=RDiXgAUi02RW4zCR_yqD27Q1vn9PenrG9B0mSav6GbI,825
torchvision\prototype\datasets\_builtin\clevr.py,sha256=mSFHuYXWh6IBBnFl2OZg6c8R0K9FzMikZIxGrsRW9cU,3706
torchvision\prototype\datasets\_builtin\coco.categories,sha256=w7x2InmPfwq_K8nSTbMrOPjkpEFr_T8m5nAwhgkA390,1443
torchvision\prototype\datasets\_builtin\coco.py,sha256=nc-Dsq7HXaeEcqqhQglQNx6ahMq3ZrjJElFrjj19fn8,10387
torchvision\prototype\datasets\_builtin\country211.categories,sha256=I8cKMrlZ_qdH8NI6_T5qkay1XkAGI_G1tui5l-_xqoE,844
torchvision\prototype\datasets\_builtin\country211.py,sha256=zZyx6CVk7-3eSTVetpL9DXNvj4DVT9zlBlf1HqJn984,2740
torchvision\prototype\datasets\_builtin\cub200.categories,sha256=f5qFtq_FWoFUXY6Zs9q7aBaJxvChO1hDVpWuKEB08q0,3532
torchvision\prototype\datasets\_builtin\cub200.py,sha256=P-PVTdSqAQ25eMplyvSgM4Vu17r8vrD4uc5hbiNdEPo,9595
torchvision\prototype\datasets\_builtin\dtd.categories,sha256=Q6anHvqMxN1qv05DUEkiVhxGD6-d5GPXYjkRJD2ZCLc,441
torchvision\prototype\datasets\_builtin\dtd.py,sha256=FGR5FJC54KhQaRjFAfCGyEzwhDCfTjgRoGP-ZqBP_sI,4783
torchvision\prototype\datasets\_builtin\eurosat.py,sha256=S7egwUJazsh3wNW9elnW28wXjiYO4vLLS-hZtJSsjK4,2118
torchvision\prototype\datasets\_builtin\fer2013.py,sha256=U4eyuo7JqX9jq73t0bpj7zAqB2Fa3VvmPILTDE4_0v8,2505
torchvision\prototype\datasets\_builtin\food101.categories,sha256=sUMSYDA10YVLxyghp8cDvvQvWvlM7m1x5Dl3Jyz6TKA,1285
torchvision\prototype\datasets\_builtin\food101.py,sha256=VywPWUTaMGG_SQnU65Uae3YHsNIiWfqfgyIn_EycYJ8,3517
torchvision\prototype\datasets\_builtin\gtsrb.py,sha256=f-6UowPUyLZYc-jLCPnRRiB54yGit_G4Fw8kPwKXXSM,4171
torchvision\prototype\datasets\_builtin\imagenet.categories,sha256=teq2DhM4XaYFhDfdcmmgqG5qZvLuhFQIYZieJyUE54Y,21488
torchvision\prototype\datasets\_builtin\imagenet.py,sha256=lV8yk6uQ20sVqsWpJwAoKgwJJEs9JVKfM3c1QwYeCHE,8314
torchvision\prototype\datasets\_builtin\mnist.py,sha256=HcvC_mb7YpMPTTrmxdrjc9qEcC6AfxFCYAg0f5zK6Ns,15584
torchvision\prototype\datasets\_builtin\oxford-iiit-pet.categories,sha256=NCtfnYuhQPSkvUNVJKbEo7ClkpG7kiKrYxsqtf3TYU0,506
torchvision\prototype\datasets\_builtin\oxford_iiit_pet.py,sha256=JA2wmKzkYm4l75MYJlMbaHqg9NW_VgcZlDyDar9flZY,5791
torchvision\prototype\datasets\_builtin\pcam.py,sha256=8RqtFy6tNInGNvabeDC73VGK2xdbU-OoIEdxgTL9P3A,4891
torchvision\prototype\datasets\_builtin\sbd.categories,sha256=gj_P20X71AJGFCtrBAcWXkkf3aTTmgGIPi7onwwnNOA,155
torchvision\prototype\datasets\_builtin\sbd.py,sha256=NZRZTCxof9fSwvJFj62sQGeURw0Mu8Q3Mv8qPVzk_p8,5817
torchvision\prototype\datasets\_builtin\semeion.py,sha256=e_CW_HmO_XhpZlFVsII6AnyMAv1lwA9M_Mv1EF0oZjY,2040
torchvision\prototype\datasets\_builtin\stanford-cars.categories,sha256=ggt3mAwzYy2emjSNCStkO11zXQxoxprEOvyZxf4rYiA,5650
torchvision\prototype\datasets\_builtin\stanford_cars.py,sha256=HjWBhM80-ZdxZrj54NaTYX-9tTfrwJ0z5ov4W4qjnxU,4518
torchvision\prototype\datasets\_builtin\svhn.py,sha256=AUbzflqH875Cp5CgAmi2DfXHg7aI02vSM0-r_C_VVCQ,2877
torchvision\prototype\datasets\_builtin\usps.py,sha256=BKeOGL0RXImS4cjoCC3p7QvvrhdN3gch2KSHcA4yFwE,2489
torchvision\prototype\datasets\_builtin\voc.categories,sha256=M_QJwWCMr3OOawdB-AmVUuPTaOTGTRyceSwmc8aWc8Y,171
torchvision\prototype\datasets\_builtin\voc.py,sha256=zqCon6vLQDn8ui9lPYmKip4Cht6myqYrLdH-5Ua0TyU,9546
torchvision\prototype\datasets\_folder.py,sha256=xw2GU3Z2-iTHeMsf7KFQbKgsBjm757ja-G6S_1pLSDE,2558
torchvision\prototype\datasets\_home.py,sha256=2IZz9Q2lGeHVLRphTLp-JDRqUyiIg9IGggFl77rBTRg,676
torchvision\prototype\datasets\benchmark.py,sha256=4e5i1p0SnHmTXEcElBjLFXSND9236CnKqZCRzYkhE1U,22221
torchvision\prototype\datasets\generate_category_files.py,sha256=AcqhyQkLNoa1l0s_C-fzxaTMLADSlo7Y66ibNoUPwJA,1677
torchvision\prototype\datasets\utils\__init__.py,sha256=UWZiJQ2AZlLln6WK_Qpr7E_OQqS5XpfaBsrgpUILe5s,237
torchvision\prototype\datasets\utils\_dataset.py,sha256=Bq3TYdeIFLST001UDNqCsW42Mvx6k6_cT-3B9FPs50E,1976
torchvision\prototype\datasets\utils\_encoded.py,sha256=0_aOYl4meWr1Emn3aK29hOFHSabpf_dMcl1NAKFgV3g,1939
torchvision\prototype\datasets\utils\_internal.py,sha256=qRI98Sab2Z-0wIMdnA2psBaIJf1FRRDldztuyKQyvPA,6704
torchvision\prototype\datasets\utils\_resource.py,sha256=egOhIVWkVheXn_qt855-h_aJ7yp1pGAp7A2bqqQOyTg,8594
torchvision\prototype\models\__init__.py,sha256=ZcKkotg_rCpLiY1iH4yWCfgF4pVoqC1eT8oUenuXCcQ,21
torchvision\prototype\models\depth\__init__.py,sha256=Zohl1Dy0JyCwHmGz10iqljPuNmuhjGcerB3KN4HLiIw,22
torchvision\prototype\models\depth\stereo\__init__.py,sha256=FbuL-euu3pNVLmRWrwdAdDn0Cw9wPhVa6cE3bLv75oU,54
torchvision\prototype\models\depth\stereo\crestereo.py,sha256=tCq3Sf34LnpdzaL03KeIznTYRaPiZkHTilkYYolUAac,66097
torchvision\prototype\models\depth\stereo\raft_stereo.py,sha256=zAu-gCEkkDBMKskNvmg268ai6aVczcnknuJrAVSZupI,38007
torchvision\prototype\transforms\__init__.py,sha256=5E6bttsVC5Ihpg3JMneKCH5g16N5m0TSqVcAhXM-6dE,236
torchvision\prototype\transforms\_augment.py,sha256=g86XukTV_HqJKBDkxHVYOEklnJ4Ua3An3dXkWADZ9oU,9161
torchvision\prototype\transforms\_geometry.py,sha256=GC_BF_aLaaL9X-5aU-Alayb_dR7tITEuMgGQkFIMaSE,4794
torchvision\prototype\transforms\_misc.py,sha256=XdfjTCAj5toSoWcw3OttMBVP5c9oDZIcsNW90X1LcW8,2818
torchvision\prototype\transforms\_presets.py,sha256=LOiasJ407QADp5YXhSjAB5JP0KT7G6P8OTbNXKTUX04,3281
torchvision\prototype\transforms\_type_conversion.py,sha256=vRVFkmDXQf50eh5s-cytRsT5PVtK2XqPh32qprIBxho,1022
torchvision\prototype\tv_tensors\__init__.py,sha256=gbnrMBnyVPuVBsNifDJQ2zUHfCDOQ3hzCvZA_0sRvmM,40
torchvision\prototype\tv_tensors\_label.py,sha256=O_sVBRJV_wRqUA_nivoy0_ui9ym3kKmwWTZqxRRIc1M,2178
torchvision\prototype\utils\__init__.py,sha256=rAgUfIBL0ZdJTgtLuS0RQA0rArO8xTaOSmav8qe_PhQ,25
torchvision\prototype\utils\_internal.py,sha256=3KgJfwtxfsUAh29jP5KJ3E78K_onOiGzmF2wMjb6yM0,5407
torchvision\transforms\__init__.py,sha256=WCNXTJUbJ1h7YaN9UfrBSvt--ST2PAV4sLICbTS-L5A,55
torchvision\transforms\_functional_pil.py,sha256=a9b6BkJnMAL2tGLknE-iQY8TQcBcaPSK54Kqml8WDsM,12463
torchvision\transforms\_functional_tensor.py,sha256=0-cJE2Ns0WpLc0hZzdC_8R3vwA8dAxdRunEnjbF6YTY,34901
torchvision\transforms\_functional_video.py,sha256=c4BbUi3Y2LvskozFdy619piLBd5acsjxgogYAXmY5P8,3971
torchvision\transforms\_presets.py,sha256=vVYoqYy3dY3j9MuLHeS8B_q5qrBJFmI1ggTBsobUeYs,8726
torchvision\transforms\_transforms_video.py,sha256=ub2gCT5ELiK918Bq-Pp6mzhWrAZxlj7blfpkA8Dhb1o,5124
torchvision\transforms\autoaugment.py,sha256=UD8UBlT4dWCIQaNDUDQBtc0osMHHPQluLr7seZJr4cY,28858
torchvision\transforms\functional.py,sha256=e_eEdiTITSrhU2aNFRp8iqenfyFxevsgrmFBAZKM_TU,69441
torchvision\transforms\transforms.py,sha256=dcHYDZIbYOW6sOywH41uOiBd9tPfEgOdfFbSzqONtfA,87700
torchvision\transforms\v2\__init__.py,sha256=d5VraJDV5PXPv5mifqUTdQx2z1p6M3ks3RNuRc-ztfk,1605
torchvision\transforms\v2\_augment.py,sha256=GcrFNbY562hfBrlL9mSn0w5x73EuU2caYgFknb68nGU,16624
torchvision\transforms\v2\_auto_augment.py,sha256=jxAX1VL_utRp83ahvWbXCbajLOCHN4GJu2W2zHktrIg,32869
torchvision\transforms\v2\_color.py,sha256=v1zDkYIrkx0gbIF-nM4EmDm-7rFR2b9PLmloxtal-hw,17369
torchvision\transforms\v2\_container.py,sha256=E-8TvTF_qBqC6aLnlK5mnp3V27oQOxhy1X7PZfQAD5w,6229
torchvision\transforms\v2\_deprecated.py,sha256=ACthGTn1ciZtp9py8njxypGEJgObcjfYJ4H4MFY8OPw,1996
torchvision\transforms\v2\_geometry.py,sha256=As5_CntSX9E1ElNuOyhD7Qa0laEllxVkfulyQI99RIg,69069
torchvision\transforms\v2\_meta.py,sha256=6CK1TP1vADvSGqrMSJcuXTRLxPMHNFmWn3Pdrlg74oc,1439
torchvision\transforms\v2\_misc.py,sha256=cUntkv21XAZhMRZY_X3Csm2Ja6fTEQs7_FHTiS4WXSQ,19553
torchvision\transforms\v2\_temporal.py,sha256=IgEq3Crm49frD7RHmv5xa9ZyQUIP9kXwVW28icQRdgY,931
torchvision\transforms\v2\_transform.py,sha256=O8cudZScPVaemfAEvW9IYnDrXCst9DdoIDd72azfqKc,9561
torchvision\transforms\v2\_type_conversion.py,sha256=2mcO70MRfpDmHMWdjI2nCdEpqxwbT0zrxy0VTuijfuE,2940
torchvision\transforms\v2\_utils.py,sha256=WCAi-6mFwQyRAcUJGhEymhDMmbdmSlsCXTljjompdZM,9100
torchvision\transforms\v2\functional\__init__.py,sha256=LGu6ZNvdGaD7gED4Mi7x-3ES1D9FnN3xraVEb_p94eI,3699
torchvision\transforms\v2\functional\_augment.py,sha256=oFPymp04zhQpTQR3O-5XUJHDnQW6-pfwWJKoP-dpWkM,3579
torchvision\transforms\v2\functional\_color.py,sha256=jnCygt9guUktJA33bepgvhV_sw5Xa7_6XNrb7JiB7vA,31144
torchvision\transforms\v2\functional\_deprecated.py,sha256=-X7agTXp-JnbFpsp3xoVk1eZr7OyT-evBt0nlULAR40,825
torchvision\transforms\v2\functional\_geometry.py,sha256=SnFMvBIgzuRBkYzGY5e-rDvcn8CXyHvKQhXzJBzq5Ag,89897
torchvision\transforms\v2\functional\_meta.py,sha256=b_MF4SQrmZNVpcN8X-8edP-CEmQ241MKDWpQTG4pmbI,10826
torchvision\transforms\v2\functional\_misc.py,sha256=q30X0WtS3uYrPoTktS-2458NFdEP1j-PG6r69HwEAtA,17937
torchvision\transforms\v2\functional\_temporal.py,sha256=tSRkkqOqUQ0QXjENF82F16El1-J0IDoFKIH-ss_cpC4,1163
torchvision\transforms\v2\functional\_type_conversion.py,sha256=oYf4LMgiClvEZwwc3WbKI7fJ-rRFhDrVSBKiPA5vxio,896
torchvision\transforms\v2\functional\_utils.py,sha256=3T5iFgq8whHQbk7duizYmoxspH6mtkP-L7ku627zfBY,5620
torchvision\tv_tensors\__init__.py,sha256=7UBIZbraVyhIO-ZCeKtD59if85XKkAQ4njjS6RjEfDg,1544
torchvision\tv_tensors\_bounding_boxes.py,sha256=t6HTK1MJ1xiiJv0miMjGezQotEw8gQNUObPFMCh0HYU,4596
torchvision\tv_tensors\_dataset_wrapper.py,sha256=pgcasnVwGiQb0mmEqPkIgycI-4AV703xbBRwUFbMo9k,25171
torchvision\tv_tensors\_image.py,sha256=wI0FrnbHUOzaIyna_Lyv5n2TGmSWwjh-HfVTC4LZklo,1987
torchvision\tv_tensors\_mask.py,sha256=soZV5aMAtzlW32Wb52ApagodZ4butgQS8C-eCIirLu0,1517
torchvision\tv_tensors\_torch_function_helpers.py,sha256=vr1G4egyQfRjUtDedTWRope1gP4OB1hzAjKFZGXfc2Y,2402
torchvision\tv_tensors\_tv_tensor.py,sha256=v-dVm-ZZs4fdT6TVcAeX7KnAJpudVUf55VqdtQtgthE,6380
torchvision\tv_tensors\_video.py,sha256=KtcHdpsm81uhx451oNs6bqFeuUw6G9XjqltDeUJNvzc,1453
torchvision\utils.py,sha256=L0XWXodEwJEy0OxpUlz5W6SYW_jLG2NwENqnwb54pQc,27972
torchvision\version.py,sha256=inmc1Iskl-r4wPE5V6b8vDNG6EXXPgPBrIjPVvsnpIA,208
torchvision\zlib.dll,sha256=jb_W73N0qDEVi93Mt549VmXpYlyBr1V_FbQVC3h39oc,99608
