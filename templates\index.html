<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemma 3n Multimodal Interface</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            display: flex;
            min-height: 600px;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            border-right: 1px solid #eee;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            max-height: 500px;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 10px;
            max-width: 80%;
        }

        .user-message {
            background: #007bff;
            color: white;
            margin-left: auto;
            text-align: right;
        }

        .assistant-message {
            background: white;
            border: 1px solid #ddd;
            margin-right: auto;
        }

        .input-section {
            padding: 20px;
            border-top: 1px solid #eee;
            background: white;
        }

        .input-form {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .text-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            resize: vertical;
            min-height: 60px;
        }

        .text-input:focus {
            outline: none;
            border-color: #007bff;
        }

        .file-inputs {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .file-input-group {
            flex: 1;
            min-width: 200px;
        }

        .file-input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }

        .file-input {
            width: 100%;
            padding: 10px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            background: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .file-input:hover {
            border-color: #007bff;
            background: #e3f2fd;
        }

        .controls {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }

        .max-tokens {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            width: 100px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        .sidebar {
            width: 300px;
            padding: 20px;
            background: #f8f9fa;
            border-left: 1px solid #eee;
        }

        .sidebar h3 {
            margin-bottom: 15px;
            color: #333;
        }

        .model-info {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #ddd;
        }

        .model-info h4 {
            margin-bottom: 10px;
            color: #007bff;
        }

        .model-info p {
            margin-bottom: 5px;
            font-size: 14px;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid #f5c6cb;
        }

        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid #c3e6cb;
        }

        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                border-left: none;
                border-top: 1px solid #eee;
            }

            .file-inputs {
                flex-direction: column;
            }

            .controls {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Gemma 3n Multimodal Interface</h1>
            <p>Interact with Google's Gemma 3n model using text, images, and audio</p>
        </div>

        <div class="main-content">
            <div class="chat-container">
                <div class="chat-messages" id="chatMessages">
                    <div class="message assistant-message">
                        <strong>Gemma 3n:</strong> Hello! I'm Gemma 3n, a multimodal AI assistant. I can help you with text, analyze images, and process audio. What would you like to explore today?
                    </div>
                </div>

                <div class="input-section">
                    <form class="input-form" id="chatForm" enctype="multipart/form-data">
                        <textarea
                            class="text-input"
                            id="textInput"
                            name="text"
                            placeholder="Type your message here... (You can also upload images or audio files)"
                            rows="3"
                        ></textarea>

                        <div class="file-inputs">
                            <div class="file-input-group">
                                <label for="imageInput">📷 Upload Image</label>
                                <input
                                    type="file"
                                    id="imageInput"
                                    name="image"
                                    accept="image/*"
                                    class="file-input"
                                >
                            </div>
                            <div class="file-input-group">
                                <label for="audioInput">🎵 Upload Audio</label>
                                <input
                                    type="file"
                                    id="audioInput"
                                    name="audio"
                                    accept="audio/*"
                                    class="file-input"
                                >
                            </div>
                        </div>

                        <div class="controls">
                            <label for="maxTokens">Max Tokens:</label>
                            <input
                                type="number"
                                id="maxTokens"
                                name="max_tokens"
                                value="256"
                                min="1"
                                max="2048"
                                class="max-tokens"
                            >
                            <button type="submit" class="btn btn-primary" id="sendBtn">
                                Send Message
                            </button>
                            <button type="button" class="btn btn-secondary" id="clearBtn">
                                Clear Chat
                            </button>
                        </div>
                    </form>

                    <div class="loading" id="loading">
                        <div class="spinner"></div>
                        <p>Processing your request...</p>
                    </div>
                </div>
            </div>

            <div class="sidebar">
                <h3>Model Information</h3>
                <div class="model-info" id="modelInfo">
                    <h4>Loading...</h4>
                    <p>Fetching model details...</p>
                </div>

                <h3>Features</h3>
                <div class="model-info">
                    <h4>Multimodal Capabilities</h4>
                    <p>✅ Text Generation</p>
                    <p>✅ Image Analysis</p>
                    <p>✅ Audio Processing</p>
                    <p>✅ Conversation Memory</p>
                </div>

                <h3>Usage Tips</h3>
                <div class="model-info">
                    <h4>Getting Started</h4>
                    <p>• Type questions or requests in the text area</p>
                    <p>• Upload images for visual analysis</p>
                    <p>• Upload audio files for transcription</p>
                    <p>• Combine text with media for rich interactions</p>
                    <p>• Adjust max tokens for longer responses</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // DOM elements
        const chatForm = document.getElementById('chatForm');
        const chatMessages = document.getElementById('chatMessages');
        const textInput = document.getElementById('textInput');
        const imageInput = document.getElementById('imageInput');
        const audioInput = document.getElementById('audioInput');
        const maxTokensInput = document.getElementById('maxTokens');
        const sendBtn = document.getElementById('sendBtn');
        const clearBtn = document.getElementById('clearBtn');
        const loading = document.getElementById('loading');
        const modelInfo = document.getElementById('modelInfo');

        // Load model information on page load
        async function loadModelInfo() {
            try {
                const response = await fetch('/model-info');
                const info = await response.json();

                modelInfo.innerHTML = `
                    <h4>${info.model_name}</h4>
                    <p><strong>Device:</strong> ${info.device}</p>
                    <p><strong>Status:</strong> ${info.loaded ? '✅ Loaded' : '❌ Not Loaded'}</p>
                    <p><strong>Context:</strong> ${info.max_context}</p>
                    <p><strong>Supports:</strong> ${info.supports.join(', ')}</p>
                `;
            } catch (error) {
                modelInfo.innerHTML = `
                    <h4>Error</h4>
                    <p>Failed to load model information</p>
                `;
            }
        }

        // Add message to chat
        function addMessage(content, isUser = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user-message' : 'assistant-message'}`;

            if (isUser) {
                messageDiv.innerHTML = `<strong>You:</strong> ${content}`;
            } else {
                messageDiv.innerHTML = `<strong>Gemma 3n:</strong> ${content}`;
            }

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Show error message
        function showError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = message;
            chatMessages.appendChild(errorDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Show loading state
        function setLoading(isLoading) {
            loading.style.display = isLoading ? 'block' : 'none';
            sendBtn.disabled = isLoading;
            sendBtn.textContent = isLoading ? 'Processing...' : 'Send Message';
        }

        // Handle form submission
        chatForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            const text = textInput.value.trim();
            const imageFile = imageInput.files[0];
            const audioFile = audioInput.files[0];
            const maxTokens = maxTokensInput.value;

            // Validate input
            if (!text && !imageFile && !audioFile) {
                showError('Please provide text, image, or audio input.');
                return;
            }

            // Create user message content
            let userContent = '';
            if (text) userContent += text;
            if (imageFile) userContent += ` [Image: ${imageFile.name}]`;
            if (audioFile) userContent += ` [Audio: ${audioFile.name}]`;

            addMessage(userContent, true);
            setLoading(true);

            try {
                // Create FormData
                const formData = new FormData();
                formData.append('text', text);
                formData.append('max_tokens', maxTokens);

                if (imageFile) {
                    formData.append('image', imageFile);
                }

                if (audioFile) {
                    formData.append('audio', audioFile);
                }

                // Send request
                const response = await fetch('/chat', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (response.ok) {
                    addMessage(result.response);
                } else {
                    showError(result.detail || 'An error occurred');
                }

            } catch (error) {
                showError('Network error: ' + error.message);
            } finally {
                setLoading(false);

                // Clear form
                textInput.value = '';
                imageInput.value = '';
                audioInput.value = '';
            }
        });

        // Handle clear chat
        clearBtn.addEventListener('click', async () => {
            try {
                const response = await fetch('/clear', { method: 'POST' });

                if (response.ok) {
                    chatMessages.innerHTML = `
                        <div class="message assistant-message">
                            <strong>Gemma 3n:</strong> Chat cleared! How can I help you today?
                        </div>
                    `;
                }
            } catch (error) {
                showError('Failed to clear chat: ' + error.message);
            }
        });

        // Handle Enter key in textarea
        textInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                chatForm.dispatchEvent(new Event('submit'));
            }
        });

        // File input change handlers
        imageInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                const label = e.target.previousElementSibling;
                label.textContent = `📷 ${file.name}`;
            }
        });

        audioInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                const label = e.target.previousElementSibling;
                label.textContent = `🎵 ${file.name}`;
            }
        });

        // Load model info on page load
        loadModelInfo();
    </script>
</body>
</html>