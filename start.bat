@echo off
REM Gemma 3n Docker Deployment Script for Windows
REM This script helps you deploy the Gemma 3n model with a web interface

echo 🚀 Gemma 3n Docker Deployment
echo ==============================

REM Check if Docker is installed
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not installed. Please install Docker Desktop first.
    pause
    exit /b 1
)

REM Check if Docker Compose is installed
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Compose is not installed. Please install Docker Compose first.
    pause
    exit /b 1
)

echo ✅ Docker and Docker Compose found

REM Create necessary directories
echo 📁 Creating directories...
if not exist "uploads" mkdir uploads
if not exist "logs" mkdir logs
if not exist "static" mkdir static

REM Check if model files exist
if not exist "config.json" (
    echo ❌ Model files not found. Please ensure all Gemma 3n model files are in this directory.
    pause
    exit /b 1
)

if not exist "tokenizer.model" (
    echo ❌ Tokenizer files not found. Please ensure all Gemma 3n model files are in this directory.
    pause
    exit /b 1
)

echo ✅ Model files found

REM Build and start the container
echo 🔨 Building Docker image...
docker-compose up --build -d

if %errorlevel% neq 0 (
    echo ❌ Failed to start the service. Check the error messages above.
    pause
    exit /b 1
)

echo ⏳ Waiting for the service to start...
timeout /t 15 /nobreak >nul

REM Check if the service is running
curl -f http://localhost:8080/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Service is running successfully!
    echo.
    echo 🌐 Access the web interface at: http://localhost:8080
    echo 📊 Health check endpoint: http://localhost:8080/health
    echo ℹ️  Model info endpoint: http://localhost:8080/model-info
    echo.
    echo 📋 Useful commands:
    echo    View logs: docker-compose logs -f
    echo    Stop service: docker-compose down
    echo    Restart service: docker-compose restart
    echo.
    echo Press any key to open the web interface...
    pause >nul
    start http://localhost:8080
) else (
    echo ❌ Service failed to start. Check the logs:
    docker-compose logs
    pause
)