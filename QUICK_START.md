# 🚀 Gemma 3n Quick Start Guide

## One-Command Deployment

### Option 1: Automated Script (Recommended)
```bash
# Run the complete deployment and testing script
python deploy_and_test.py
```

### Option 2: Windows Batch File
```cmd
# Double-click or run in Command Prompt
start.bat
```

### Option 3: Linux/macOS Shell Script
```bash
chmod +x start.sh
./start.sh
```

### Option 4: Manual Docker Compose
```bash
docker-compose up --build -d
```

## Access Your AI

Once deployed, open your browser and go to:
**http://localhost:8080**

## Quick Test

1. **Text Chat**: Type "Hello! Tell me a joke" and press Enter
2. **Image Upload**: Click "📷 Upload Image" and select a photo
3. **Audio Upload**: Click "🎵 Upload Audio" and select an audio file
4. **Multimodal**: Combine text with images or audio for rich interactions

## Troubleshooting

### Service Not Starting?
```bash
# Check logs
docker-compose logs -f

# Check container status
docker-compose ps
```

### Model Not Loading?
```bash
# Test model files
python test_model.py

# Check health
curl http://localhost:8080/health
```

### API Issues?
```bash
# Test all endpoints
python test_api.py

# Manual health check
python healthcheck.py
```

## Stop the Service

```bash
docker-compose down
```

## Need Help?

- 📚 Full documentation: `DEPLOYMENT_README.md`
- 🔧 Test scripts: `test_model.py`, `test_api.py`
- 🏥 Health check: `healthcheck.py`
- 📊 Model info: http://localhost:8080/model-info

---

**That's it! Your Gemma 3n multimodal AI is ready to use! 🎉**