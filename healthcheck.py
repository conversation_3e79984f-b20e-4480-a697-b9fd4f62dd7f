#!/usr/bin/env python3
"""
Health check script for Docker container
"""

import sys
import requests
import time

def check_health():
    """Check if the web service is healthy"""
    try:
        # Check health endpoint
        response = requests.get("http://localhost:8080/health", timeout=10)

        if response.status_code == 200:
            data = response.json()
            if data.get("status") == "healthy" and data.get("model_loaded"):
                print("✅ Service is healthy and model is loaded")
                return True
            else:
                print(f"❌ Service unhealthy: {data}")
                return False
        else:
            print(f"❌ Health check failed with status {response.status_code}")
            return False

    except requests.exceptions.RequestException as e:
        print(f"❌ Health check failed: {e}")
        return False

def main():
    """Main health check function"""
    max_retries = 3
    retry_delay = 2

    for attempt in range(max_retries):
        if attempt > 0:
            print(f"Retry {attempt}/{max_retries-1} after {retry_delay}s...")
            time.sleep(retry_delay)

        if check_health():
            sys.exit(0)

    print(f"❌ Health check failed after {max_retries} attempts")
    sys.exit(1)

if __name__ == "__main__":
    main()