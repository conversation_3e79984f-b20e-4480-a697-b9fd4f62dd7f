#!/usr/bin/env python3
"""
Test script to verify Gemma 3n model loading and basic functionality
"""

import os
import sys
import torch
from pathlib import Path

def test_model_files():
    """Check if all required model files are present"""
    print("🔍 Checking model files...")

    required_files = [
        "config.json",
        "tokenizer.model",
        "tokenizer_config.json",
        "processor_config.json",
        "preprocessor_config.json",
        "special_tokens_map.json",
        "model.safetensors.index.json"
    ]

    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
        else:
            print(f"  ✅ {file}")

    # Check for model weight files
    model_files = list(Path(".").glob("model-*.safetensors"))
    if not model_files:
        missing_files.append("model-*.safetensors")
    else:
        print(f"  ✅ Found {len(model_files)} model weight files")

    if missing_files:
        print(f"❌ Missing files: {missing_files}")
        return False

    print("✅ All model files present")
    return True

def test_dependencies():
    """Test if all required dependencies are available"""
    print("\n🔍 Checking dependencies...")

    dependencies = [
        ("torch", "PyTorch"),
        ("transformers", "Transformers"),
        ("fastapi", "FastAPI"),
        ("uvicorn", "Uvicorn"),
        ("PIL", "Pillow"),
        ("librosa", "Librosa"),
        ("soundfile", "SoundFile"),
        ("numpy", "NumPy")
    ]

    missing_deps = []
    for module, name in dependencies:
        try:
            __import__(module)
            print(f"  ✅ {name}")
        except ImportError:
            missing_deps.append(name)
            print(f"  ❌ {name}")

    if missing_deps:
        print(f"❌ Missing dependencies: {missing_deps}")
        print("Install with: pip install -r requirements.txt")
        return False

    print("✅ All dependencies available")
    return True

def test_gpu_availability():
    """Check GPU availability"""
    print("\n🔍 Checking GPU availability...")

    if torch.cuda.is_available():
        gpu_count = torch.cuda.device_count()
        current_device = torch.cuda.current_device()
        gpu_name = torch.cuda.get_device_name(current_device)

        print(f"  ✅ CUDA available")
        print(f"  ✅ GPU count: {gpu_count}")
        print(f"  ✅ Current device: {current_device}")
        print(f"  ✅ GPU name: {gpu_name}")

        # Test GPU memory
        try:
            memory_allocated = torch.cuda.memory_allocated(current_device)
            memory_cached = torch.cuda.memory_reserved(current_device)
            print(f"  ✅ Memory allocated: {memory_allocated / 1024**2:.1f} MB")
            print(f"  ✅ Memory cached: {memory_cached / 1024**2:.1f} MB")
        except:
            print("  ⚠️  Could not get GPU memory info")

        return True
    else:
        print("  ⚠️  CUDA not available, will use CPU")
        return False

def test_model_loading():
    """Test basic model loading"""
    print("\n🔍 Testing model loading...")

    try:
        from transformers import AutoProcessor, AutoModelForImageTextToText

        print("  📥 Loading processor...")
        processor = AutoProcessor.from_pretrained(".")
        print("  ✅ Processor loaded successfully")

        print("  📥 Loading model...")
        device = "cuda" if torch.cuda.is_available() else "cpu"

        model = AutoModelForImageTextToText.from_pretrained(
            ".",
            torch_dtype=torch.bfloat16 if device == "cuda" else torch.float32,
            device_map="auto" if device == "cuda" else None
        )

        if device == "cpu":
            model = model.to(device)

        print(f"  ✅ Model loaded successfully on {device}")
        print(f"  ✅ Model dtype: {model.dtype}")

        # Test basic inference
        print("  🧪 Testing basic inference...")
        messages = [
            {
                "role": "user",
                "content": [{"type": "text", "text": "Hello, how are you?"}]
            }
        ]

        inputs = processor.apply_chat_template(
            messages,
            add_generation_prompt=True,
            tokenize=True,
            return_dict=True,
            return_tensors="pt",
        )

        inputs = inputs.to(model.device)

        with torch.inference_mode():
            outputs = model.generate(**inputs, max_new_tokens=50, do_sample=False)

        response = processor.batch_decode(
            outputs[:, inputs["input_ids"].shape[-1]:],
            skip_special_tokens=True,
            clean_up_tokenization_spaces=True
        )

        print(f"  ✅ Test response: {response[0][:100]}...")
        print("✅ Model loading and inference test passed!")
        return True

    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Gemma 3n Model Test Suite")
    print("=" * 40)

    tests = [
        test_model_files,
        test_dependencies,
        test_gpu_availability,
        test_model_loading
    ]

    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append(False)

    print("\n" + "=" * 40)
    print("📊 Test Results:")

    test_names = [
        "Model Files",
        "Dependencies",
        "GPU Availability",
        "Model Loading"
    ]

    for name, result in zip(test_names, results):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {name}: {status}")

    if all(results[:2]):  # Model files and dependencies are critical
        print("\n🎉 Ready for deployment!")
        return True
    else:
        print("\n⚠️  Please fix the issues above before deployment.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)