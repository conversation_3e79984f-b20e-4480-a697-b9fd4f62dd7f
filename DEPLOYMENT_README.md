# Gemma 3n Multimodal Docker Deployment

This repository contains a complete Docker deployment solution for Google's Gemma 3n multimodal AI model with a user-friendly web interface.

## 🚀 Features

- **Multimodal AI Interface**: Support for text, image, and audio inputs
- **Modern Web UI**: Responsive, intuitive interface built with FastAPI and HTML/CSS/JavaScript
- **Docker Containerization**: Easy deployment with GPU support
- **Real-time Chat**: Interactive conversation with the AI model
- **File Upload Support**: Drag-and-drop image and audio file processing
- **Health Monitoring**: Built-in health checks and monitoring endpoints

## 📋 Prerequisites

### System Requirements
- **GPU**: NVIDIA GPU with CUDA support (recommended)
- **RAM**: Minimum 16GB, 32GB+ recommended
- **Storage**: At least 20GB free space for model and Docker images
- **OS**: Linux (Ubuntu 20.04+), Windows with WSL2, or macOS

### Software Requirements
- **Docker**: Version 20.10+
- **Docker Compose**: Version 2.0+
- **NVIDIA Container Toolkit** (for GPU support)

## 🛠️ Installation

### 1. Install Docker and NVIDIA Container Toolkit

#### Ubuntu/Debian:
```bash
# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Install NVIDIA Container Toolkit
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list

sudo apt-get update && sudo apt-get install -y nvidia-docker2
sudo systemctl restart docker
```

#### Windows (WSL2):
1. Install Docker Desktop with WSL2 backend
2. Install NVIDIA drivers for WSL2
3. Enable GPU support in Docker Desktop settings

### 2. Clone and Prepare the Model

Ensure your directory structure looks like this:
```
Gemma/
├── Dockerfile
├── docker-compose.yml
├── DEPLOYMENT_README.md
├── README.md
├── config.json
├── model-*.safetensors
├── tokenizer.*
├── web_app/
│   └── main.py
├── templates/
│   └── index.html
├── static/
└── uploads/
```

## 🚀 Quick Start

### Option 1: Using Docker Compose (Recommended)

1. **Build and start the container:**
```bash
docker-compose up --build
```

2. **Access the web interface:**
Open your browser and navigate to: `http://localhost:8080`

### Option 2: Using Docker directly

1. **Build the Docker image:**
```bash
docker build -t gemma-3n-web .
```

2. **Run the container:**
```bash
# With GPU support
docker run -d \
  --name gemma-3n-container \
  --gpus all \
  -p 8080:8080 \
  -v $(pwd)/uploads:/app/uploads \
  gemma-3n-web

# CPU only (slower)
docker run -d \
  --name gemma-3n-container \
  -p 8080:8080 \
  -v $(pwd)/uploads:/app/uploads \
  gemma-3n-web
```

3. **Access the web interface:**
Open your browser and navigate to: `http://localhost:8080`

## 🎯 Usage Guide

### Web Interface Features

1. **Text Chat**: Type messages in the text area and press Enter or click "Send Message"
2. **Image Analysis**: Upload images (JPG, PNG, etc.) for visual analysis
3. **Audio Processing**: Upload audio files (WAV, MP3, etc.) for transcription and analysis
4. **Multimodal Queries**: Combine text with images and/or audio for rich interactions
5. **Conversation History**: The model maintains context throughout the conversation
6. **Adjustable Parameters**: Control response length with the "Max Tokens" setting

### API Endpoints

The application exposes several REST API endpoints:

- `GET /`: Main web interface
- `POST /chat`: Send multimodal messages to the AI
- `POST /clear`: Clear conversation history
- `GET /health`: Health check endpoint
- `GET /model-info`: Get model information and status

### Example API Usage

```bash
# Text-only request
curl -X POST "http://localhost:8080/chat" \
  -F "text=Hello, how are you?" \
  -F "max_tokens=256"

# Multimodal request with image
curl -X POST "http://localhost:8080/chat" \
  -F "text=What do you see in this image?" \
  -F "image=@/path/to/image.jpg" \
  -F "max_tokens=512"
```

## 🔧 Configuration

### Environment Variables

You can customize the deployment using environment variables:

```bash
# GPU device selection
CUDA_VISIBLE_DEVICES=0

# Model configuration
MODEL_PATH=/app
MAX_FILE_SIZE=52428800  # 50MB in bytes

# Server configuration
HOST=0.0.0.0
PORT=8080
```

### Docker Compose Customization

Edit `docker-compose.yml` to customize:

```yaml
services:
  gemma-3n-web:
    environment:
      - CUDA_VISIBLE_DEVICES=0,1  # Use multiple GPUs
    ports:
      - "3000:8080"  # Change external port
    volumes:
      - ./custom-uploads:/app/uploads  # Custom upload directory
```

## 📊 Monitoring and Logs

### Health Checks

The application includes built-in health monitoring:

```bash
# Check application health
curl http://localhost:8080/health

# Check model status
curl http://localhost:8080/model-info
```

### View Logs

```bash
# Docker Compose logs
docker-compose logs -f

# Direct Docker logs
docker logs -f gemma-3n-container
```

## 🛠️ Troubleshooting

### Common Issues

1. **GPU Not Detected**
   ```bash
   # Verify NVIDIA Docker support
   docker run --rm --gpus all nvidia/cuda:11.8-base-ubuntu22.04 nvidia-smi
   ```

2. **Out of Memory Errors**
   - Reduce max_tokens parameter
   - Use CPU-only mode for smaller systems
   - Close other GPU-intensive applications

3. **Model Loading Fails**
   - Ensure all model files are present
   - Check file permissions
   - Verify sufficient disk space

4. **Port Already in Use**
   ```bash
   # Change port in docker-compose.yml or use different port
   docker-compose down
   # Edit docker-compose.yml to use different port
   docker-compose up
   ```

### Performance Optimization

1. **GPU Memory Management**
   - Monitor GPU usage: `nvidia-smi`
   - Adjust batch sizes if needed
   - Use mixed precision training

2. **CPU Performance**
   - Increase Docker memory limits
   - Use SSD storage for model files
   - Optimize container resource allocation

## 🔒 Security Considerations

1. **File Upload Security**
   - File size limits are enforced (50MB default)
   - Only specific file types are accepted
   - Uploaded files are processed in isolated environment

2. **Network Security**
   - Run behind reverse proxy for production
   - Use HTTPS in production environments
   - Implement authentication if needed

## 📝 Development

### Local Development Setup

1. **Install Python dependencies:**
```bash
pip install -r requirements.txt
```

2. **Run development server:**
```bash
cd web_app
python main.py
```

### Customizing the Interface

- **Frontend**: Edit `templates/index.html`
- **Styling**: Modify CSS in the `<style>` section
- **Backend**: Update `web_app/main.py`
- **API**: Add new endpoints in the FastAPI application

## 📄 License

This deployment setup is provided under the same license as the Gemma model. Please refer to the original Gemma license terms.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

For issues related to:
- **Model functionality**: Refer to Google's Gemma documentation
- **Deployment setup**: Create an issue in this repository
- **Docker problems**: Check Docker and NVIDIA Container Toolkit documentation

---

**Happy AI chatting! 🤖✨**