#!/usr/bin/env python3
"""
Complete deployment and testing script for Gemma 3n Docker deployment
"""

import sys
import subprocess
import time
import requests
from pathlib import Path

def run_command(cmd, cwd=None, check=True):
    """Run a shell command and return the result"""
    try:
        result = subprocess.run(
            cmd,
            shell=True,
            cwd=cwd,
            capture_output=True,
            text=True,
            check=check
        )
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.CalledProcessError as e:
        return False, e.stdout, e.stderr

def check_prerequisites():
    """Check if all prerequisites are installed"""
    print("🔍 Checking prerequisites...")

    # Check Docker
    success, stdout, _ = run_command("docker --version", check=False)
    if not success:
        print("❌ Docker not found. Please install Docker first.")
        return False
    print(f"  ✅ Docker: {stdout.strip()}")

    # Check Docker Compose
    success, stdout, _ = run_command("docker-compose --version", check=False)
    if not success:
        print("❌ Docker Compose not found. Please install Docker Compose first.")
        return False
    print(f"  ✅ Docker Compose: {stdout.strip()}")

    # Check model files
    required_files = [
        "config.json",
        "tokenizer.model",
        "model.safetensors.index.json"
    ]

    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)

    if missing_files:
        print(f"❌ Missing model files: {missing_files}")
        return False

    print("  ✅ Model files present")
    return True

def build_and_deploy():
    """Build and deploy the Docker container"""
    print("\n🔨 Building and deploying...")

    # Stop any existing containers
    print("  🛑 Stopping existing containers...")
    run_command("docker-compose down", check=False)

    # Build and start
    print("  🏗️  Building Docker image...")
    success, _, stderr = run_command("docker-compose up --build -d")

    if not success:
        print(f"❌ Build failed: {stderr}")
        return False

    print("  ✅ Container started successfully")
    return True

def wait_for_service(max_wait=180):
    """Wait for the service to be ready"""
    print(f"\n⏳ Waiting for service to be ready (max {max_wait}s)...")

    start_time = time.time()
    while time.time() - start_time < max_wait:
        try:
            response = requests.get("http://localhost:8080/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "healthy" and data.get("model_loaded"):
                    print("  ✅ Service is ready and model is loaded!")
                    return True
        except:
            pass

        elapsed = int(time.time() - start_time)
        print(f"  ⏳ Still waiting... ({elapsed}s)")
        time.sleep(10)

    print("❌ Service not ready within timeout")
    return False

def run_tests():
    """Run comprehensive tests"""
    print("\n🧪 Running tests...")

    # Run model test
    print("  📋 Running model tests...")
    success, _, stderr = run_command("python test_model.py", check=False)
    if success:
        print("  ✅ Model tests passed")
    else:
        print(f"  ⚠️  Model tests had issues: {stderr}")

    # Run API tests
    print("  📋 Running API tests...")
    success, _, stderr = run_command("python test_api.py", check=False)
    if success:
        print("  ✅ API tests passed")
    else:
        print(f"  ❌ API tests failed: {stderr}")
        return False

    return True

def show_deployment_info():
    """Show deployment information"""
    print("\n🎉 Deployment Complete!")
    print("=" * 50)
    print("🌐 Web Interface: http://localhost:8080")
    print("📊 Health Check: http://localhost:8080/health")
    print("ℹ️  Model Info: http://localhost:8080/model-info")
    print("📚 API Docs: http://localhost:8080/docs")
    print("")
    print("📋 Useful Commands:")
    print("  View logs: docker-compose logs -f")
    print("  Stop service: docker-compose down")
    print("  Restart: docker-compose restart")
    print("  Check status: docker-compose ps")
    print("")
    print("🔧 Test Commands:")
    print("  Test model: python test_model.py")
    print("  Test API: python test_api.py")
    print("  Health check: python healthcheck.py")

def main():
    """Main deployment function"""
    print("🚀 Gemma 3n Complete Deployment Script")
    print("=" * 50)

    # Check prerequisites
    if not check_prerequisites():
        print("\n❌ Prerequisites check failed. Please fix the issues above.")
        sys.exit(1)

    # Build and deploy
    if not build_and_deploy():
        print("\n❌ Deployment failed. Check the error messages above.")
        sys.exit(1)

    # Wait for service
    if not wait_for_service():
        print("\n❌ Service failed to start properly.")
        print("Check logs with: docker-compose logs")
        sys.exit(1)

    # Run tests
    if not run_tests():
        print("\n⚠️  Some tests failed, but service is running.")
        print("You can still access the web interface at http://localhost:8080")

    # Show info
    show_deployment_info()

    print("\n✨ Ready to use! Open http://localhost:8080 in your browser.")

if __name__ == "__main__":
    main()