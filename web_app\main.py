#!/usr/bin/env python3
"""
Gemma 3n Multimodal Web Interface
A FastAPI web application for interacting with the Gemma 3n multimodal model
"""

import os
import io
import base64
import tempfile
from typing import Optional, List
from pathlib import Path

import torch
import uvicorn
from fastapi import FastAP<PERSON>, File, UploadFile, Form, HTTPException, Request
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from transformers import AutoProcessor, AutoModelForImageTextToText
from PIL import Image
import librosa
import soundfile as sf
import numpy as np

# Initialize FastAPI app
app = FastAPI(
    title="Gemma 3n Multimodal Interface",
    description="Web interface for Gemma 3n multimodal AI model",
    version="1.0.0"
)

# Global variables for model and processor
model = None
processor = None
device = None

# Configuration
MODEL_PATH = "/app"
UPLOAD_DIR = "/app/uploads"
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB

# Ensure upload directory exists
os.makedirs(UPLOAD_DIR, exist_ok=True)

# Mount static files and templates
app.mount("/static", StaticFiles(directory="/app/static"), name="static")
templates = Jinja2Templates(directory="/app/templates")


class ChatState:
    """Chat state management for conversation history"""
    def __init__(self, model, processor):
        self.model = model
        self.processor = processor
        self.history = []

    def send_message(self, message, max_tokens=256):
        """Send a message and get response"""
        self.history.append(message)

        input_ids = self.processor.apply_chat_template(
            self.history,
            add_generation_prompt=True,
            tokenize=True,
            return_dict=True,
            return_tensors="pt",
        )
        input_len = input_ids["input_ids"].shape[-1]

        input_ids = input_ids.to(self.model.device, dtype=self.model.dtype)
        outputs = self.model.generate(
            **input_ids,
            max_new_tokens=max_tokens,
            disable_compile=True
        )
        text = self.processor.batch_decode(
            outputs[:, input_len:],
            skip_special_tokens=True,
            clean_up_tokenization_spaces=True
        )

        response = {
            "role": "assistant",
            "content": [{"type": "text", "text": text[0]}]
        }
        self.history.append(response)
        return text[0]

    def clear_history(self):
        """Clear conversation history"""
        self.history = []


# Global chat state
chat_state = None


@app.on_event("startup")
async def startup_event():
    """Initialize model on startup"""
    global model, processor, device, chat_state

    print("Loading Gemma 3n model...")

    # Determine device
    if torch.cuda.is_available():
        device = "cuda"
        print(f"Using GPU: {torch.cuda.get_device_name()}")
    else:
        device = "cpu"
        print("Using CPU")

    try:
        # Load processor and model
        processor = AutoProcessor.from_pretrained(MODEL_PATH)
        model = AutoModelForImageTextToText.from_pretrained(
            MODEL_PATH,
            torch_dtype=torch.bfloat16 if device == "cuda" else torch.float32,
            device_map="auto" if device == "cuda" else None
        )

        if device == "cpu":
            model = model.to(device)

        model.eval()

        # Initialize chat state
        chat_state = ChatState(model, processor)

        print(f"Model loaded successfully on {device}")
        print(f"Model dtype: {model.dtype}")

    except Exception as e:
        print(f"Error loading model: {e}")
        raise e


@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    """Serve the main interface"""
    return templates.TemplateResponse("index.html", {"request": request})


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "model_loaded": model is not None,
        "device": device
    }


@app.post("/chat")
async def chat_endpoint(
    text: str = Form(...),
    image: Optional[UploadFile] = File(None),
    audio: Optional[UploadFile] = File(None),
    max_tokens: int = Form(256)
):
    """Main chat endpoint for multimodal interaction"""
    global chat_state

    if not model or not processor:
        raise HTTPException(status_code=503, detail="Model not loaded")

    try:
        # Build message content
        content = []

        # Add text
        if text.strip():
            content.append({"type": "text", "text": text.strip()})

        # Process image if provided
        if image:
            if image.size > MAX_FILE_SIZE:
                raise HTTPException(status_code=413, detail="Image file too large")

            # Read and process image
            image_data = await image.read()
            pil_image = Image.open(io.BytesIO(image_data))

            # Convert to RGB if necessary
            if pil_image.mode != 'RGB':
                pil_image = pil_image.convert('RGB')

            content.append({"type": "image", "image": pil_image})

        # Process audio if provided
        if audio:
            if audio.size > MAX_FILE_SIZE:
                raise HTTPException(status_code=413, detail="Audio file too large")

            # Save audio temporarily and process
            with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tmp_file:
                audio_data = await audio.read()
                tmp_file.write(audio_data)
                tmp_file.flush()

                # Load audio with librosa
                audio_array, sample_rate = librosa.load(tmp_file.name, sr=16000)
                content.append({"type": "audio", "audio": audio_array})

                # Clean up temp file
                os.unlink(tmp_file.name)

        if not content:
            raise HTTPException(status_code=400, detail="No content provided")

        # Create message
        message = {
            "role": "user",
            "content": content
        }

        # Get response from model
        response_text = chat_state.send_message(message, max_tokens)

        return {
            "response": response_text,
            "status": "success"
        }

    except Exception as e:
        print(f"Error in chat endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/clear")
async def clear_chat():
    """Clear conversation history"""
    global chat_state

    if chat_state:
        chat_state.clear_history()

    return {"status": "cleared"}


@app.get("/model-info")
async def model_info():
    """Get model information"""
    return {
        "model_name": "Gemma 3n E4B",
        "model_path": MODEL_PATH,
        "device": device,
        "supports": ["text", "image", "audio"],
        "max_context": "32K tokens",
        "loaded": model is not None
    }


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8080,
        reload=True
    )