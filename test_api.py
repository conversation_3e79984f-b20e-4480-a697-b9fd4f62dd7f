#!/usr/bin/env python3
"""
API test script for Gemma 3n web interface
"""

import requests
import time
import sys

BASE_URL = "http://localhost:8080"

def test_health():
    """Test health endpoint"""
    print("🔍 Testing health endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"  ✅ Health: {data}")
            return True
        else:
            print(f"  ❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ Health check error: {e}")
        return False

def test_model_info():
    """Test model info endpoint"""
    print("\n🔍 Testing model info endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/model-info", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"  ✅ Model info: {data}")
            return True
        else:
            print(f"  ❌ Model info failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ Model info error: {e}")
        return False

def test_text_chat():
    """Test text-only chat"""
    print("\n🔍 Testing text chat...")
    try:
        data = {
            "text": "Hello! Can you tell me a short joke?",
            "max_tokens": 100
        }

        response = requests.post(f"{BASE_URL}/chat", data=data, timeout=60)
        if response.status_code == 200:
            result = response.json()
            print(f"  ✅ Chat response: {result.get('response', '')[:100]}...")
            return True
        else:
            print(f"  ❌ Chat failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"  ❌ Chat error: {e}")
        return False

def test_clear_chat():
    """Test clear chat endpoint"""
    print("\n🔍 Testing clear chat...")
    try:
        response = requests.post(f"{BASE_URL}/clear", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"  ✅ Clear chat: {data}")
            return True
        else:
            print(f"  ❌ Clear chat failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ Clear chat error: {e}")
        return False

def test_web_interface():
    """Test main web interface"""
    print("\n🔍 Testing web interface...")
    try:
        response = requests.get(f"{BASE_URL}/", timeout=10)
        if response.status_code == 200:
            content = response.text
            if "Gemma 3n" in content and "multimodal" in content.lower():
                print("  ✅ Web interface loaded successfully")
                return True
            else:
                print("  ❌ Web interface content invalid")
                return False
        else:
            print(f"  ❌ Web interface failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ Web interface error: {e}")
        return False

def wait_for_service(max_wait=120):
    """Wait for service to be ready"""
    print(f"⏳ Waiting for service to be ready (max {max_wait}s)...")

    start_time = time.time()
    while time.time() - start_time < max_wait:
        try:
            response = requests.get(f"{BASE_URL}/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "healthy":
                    print("✅ Service is ready!")
                    return True
        except:
            pass

        print("  ⏳ Still waiting...")
        time.sleep(5)

    print("❌ Service not ready within timeout")
    return False

def main():
    """Run all API tests"""
    print("🚀 Gemma 3n API Test Suite")
    print("=" * 40)

    # Wait for service to be ready
    if not wait_for_service():
        print("❌ Service not available")
        sys.exit(1)

    tests = [
        ("Health Check", test_health),
        ("Model Info", test_model_info),
        ("Web Interface", test_web_interface),
        ("Text Chat", test_text_chat),
        ("Clear Chat", test_clear_chat)
    ]

    results = []
    for name, test_func in tests:
        try:
            result = test_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name} failed with exception: {e}")
            results.append((name, False))

    print("\n" + "=" * 40)
    print("📊 Test Results:")

    passed = 0
    for name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {name}: {status}")
        if result:
            passed += 1

    print(f"\n📈 Summary: {passed}/{len(results)} tests passed")

    if passed == len(results):
        print("🎉 All tests passed! API is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Check the logs above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)