# Gemma 3n Environment Configuration
# Copy this file to .env and modify as needed

# GPU Configuration
CUDA_VISIBLE_DEVICES=0

# Model Configuration
MODEL_PATH=/app
MAX_FILE_SIZE=52428800

# Server Configuration
HOST=0.0.0.0
PORT=8080
WORKERS=1

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# Security
MAX_UPLOAD_SIZE=50MB
ALLOWED_IMAGE_TYPES=image/jpeg,image/png,image/gif,image/webp
ALLOWED_AUDIO_TYPES=audio/wav,audio/mp3,audio/ogg,audio/flac

# Performance
MAX_TOKENS_DEFAULT=256
MAX_TOKENS_LIMIT=2048
INFERENCE_TIMEOUT=300

# Development
DEBUG=false
RELOAD=false