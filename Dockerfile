# Use NVIDIA CUDA base image for GPU support
FROM nvidia/cuda:11.8-devel-ubuntu22.04

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1
ENV CUDA_VISIBLE_DEVICES=0

# Install system dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-dev \
    git \
    wget \
    curl \
    ffmpeg \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libglib2.0-0 \
    libsndfile1 \
    && rm -rf /var/lib/apt/lists/*

# Create working directory
WORKDIR /app

# Copy model files
COPY . /app/

# Install Python dependencies
RUN pip3 install --no-cache-dir --upgrade pip && \
    pip3 install --no-cache-dir \
    torch>=2.0.0 \
    torchvision \
    torchaudio \
    transformers>=4.53.0 \
    timm>=1.0.16 \
    fastapi \
    uvicorn[standard] \
    python-multipart \
    pillow \
    librosa \
    soundfile \
    numpy \
    jinja2 \
    aiofiles \
    python-jose[cryptography] \
    passlib[bcrypt]

# Create directories for uploads and cache
RUN mkdir -p /app/uploads /app/static /app/templates

# Copy web application files
COPY web_app/ /app/web_app/
COPY static/ /app/static/
COPY templates/ /app/templates/

# Set permissions
RUN chmod +x /app/web_app/main.py

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Run the application
CMD ["python3", "-m", "uvicorn", "web_app.main:app", "--host", "0.0.0.0", "--port", "8080", "--reload"]