# Use Python base image for CPU deployment
FROM python:3.10-slim

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    wget \
    curl \
    ffmpeg \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libglib2.0-0 \
    libsndfile1 \
    && rm -rf /var/lib/apt/lists/*

# Create working directory
WORKDIR /app

# Copy model files
COPY . /app/

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir \
    torch>=2.0.0 --index-url https://download.pytorch.org/whl/cpu && \
    pip install --no-cache-dir \
    torchvision \
    torchaudio \
    transformers>=4.53.0 \
    timm>=1.0.16 \
    fastapi \
    uvicorn[standard] \
    python-multipart \
    pillow \
    librosa \
    soundfile \
    numpy \
    jinja2 \
    aiofiles \
    python-jose[cryptography] \
    passlib[bcrypt]

# Create directories for uploads and cache
RUN mkdir -p /app/uploads /app/static /app/templates

# Copy web application files
COPY web_app/ /app/web_app/
COPY static/ /app/static/
COPY templates/ /app/templates/

# Set permissions
RUN chmod +x /app/web_app/main.py

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Run the application
CMD ["python", "-m", "uvicorn", "web_app.main:app", "--host", "0.0.0.0", "--port", "8080", "--reload"]