#!/bin/bash

# Gemma 3n Docker Deployment Script
# This script helps you deploy the Gemma 3n model with a web interface

set -e

echo "🚀 Gemma 3n Docker Deployment"
echo "=============================="

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Check if NVIDIA Docker is available (optional)
if command -v nvidia-docker &> /dev/null || docker run --rm --gpus all nvidia/cuda:11.8-base-ubuntu22.04 nvidia-smi &> /dev/null; then
    echo "✅ NVIDIA Docker support detected"
    GPU_SUPPORT=true
else
    echo "⚠️  NVIDIA Docker support not detected. Running in CPU mode."
    GPU_SUPPORT=false
fi

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p uploads logs static

# Check if model files exist
if [ ! -f "config.json" ] || [ ! -f "tokenizer.model" ]; then
    echo "❌ Model files not found. Please ensure all Gemma 3n model files are in this directory."
    exit 1
fi

echo "✅ Model files found"

# Build and start the container
echo "🔨 Building Docker image..."
if [ "$GPU_SUPPORT" = true ]; then
    docker-compose up --build -d
else
    # Modify docker-compose for CPU-only deployment
    sed 's/nvidia/# nvidia/g' docker-compose.yml > docker-compose-cpu.yml
    docker-compose -f docker-compose-cpu.yml up --build -d
fi

echo "⏳ Waiting for the service to start..."
sleep 10

# Check if the service is running
if curl -f http://localhost:8080/health &> /dev/null; then
    echo "✅ Service is running successfully!"
    echo ""
    echo "🌐 Access the web interface at: http://localhost:8080"
    echo "📊 Health check endpoint: http://localhost:8080/health"
    echo "ℹ️  Model info endpoint: http://localhost:8080/model-info"
    echo ""
    echo "📋 Useful commands:"
    echo "   View logs: docker-compose logs -f"
    echo "   Stop service: docker-compose down"
    echo "   Restart service: docker-compose restart"
else
    echo "❌ Service failed to start. Check the logs:"
    docker-compose logs
fi